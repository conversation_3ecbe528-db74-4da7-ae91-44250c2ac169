#!/usr/bin/env node

/**
 * Production Benchmark Test - Uses ACTUAL database configurations and REAL data
 * This script connects to your existing databases with real data for authentic performance testing
 */

const mysql = require('mysql2/promise');
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

// Import your actual database configurations
const { getDatabaseConfig, getConnectionUrl } = require('./dist/config/database.config');

// Available tenants from your config
const AVAILABLE_TENANTS = ['devapi', 'staging3_dev'];

// Test scenarios for sp_fetch_items with real-world parameters
const PRODUCTION_TEST_SCENARIOS = [
  {
    name: 'Basic Production Fetch',
    description: 'Simple fetch without grouping - typical dashboard query',
    params: [null, null, null, null, null, 0, 0, 0, null, 1, 0, 'fab'],
    expectedUseCase: 'Dashboard loading, basic work item lists'
  },
  {
    name: 'Job-Filtered Production Query',
    description: 'Fetch items for specific jobs - common filtering scenario',
    params: [null, '1,2,3,4,5', null, null, null, 0, 0, 0, null, 1, 0, 'fab'],
    expectedUseCase: 'Job-specific work item views'
  },
  {
    name: 'Package-Filtered Production Query',
    description: 'Fetch items for specific packages - package management view',
    params: [null, null, '1,2,3,4,5', null, null, 0, 0, 0, null, 1, 0, 'fab'],
    expectedUseCase: 'Package management, progress tracking'
  },
  {
    name: 'Assignment-Based Production Query',
    description: 'Fetch assigned work items for current user - worker dashboard',
    params: [null, null, null, null, null, 0, 1, 0, 1, 1, 0, 'fab'],
    expectedUseCase: 'Worker dashboard, assigned tasks view'
  },
  {
    name: 'BOM Items Production Query',
    description: 'Fetch BOM items only - material planning',
    params: [null, null, null, null, null, 1, 0, 0, null, 1, 0, 'fab'],
    expectedUseCase: 'Material planning, BOM management'
  },
  {
    name: 'Grouped Production Query',
    description: 'Grouped by columns - reporting and analytics',
    params: ['1,2,3', null, null, null, null, 0, 0, 0, null, 1, 1, 'fab'],
    expectedUseCase: 'Reports, analytics, grouped views'
  },
  {
    name: 'Complex Production Query',
    description: 'Complex filtering with grouping - advanced reporting',
    params: ['1,2', '1,2,3', '1,2,3', null, '1,2,3', 1, 1, 0, 1, 1, 2, 'fab'],
    expectedUseCase: 'Advanced reports, complex analytics'
  },
  {
    name: 'Archive-Inclusive Query',
    description: 'Include archived items - historical data analysis',
    params: [null, null, null, null, null, 0, 0, 1, null, 1, 0, 'fab'],
    expectedUseCase: 'Historical analysis, audit trails'
  }
];

class ProductionBenchmarkTester {
  constructor(tenantId = 'devapi') {
    this.tenantId = tenantId;
    this.dbConfig = null;
    this.connections = new Map();
    this.results = [];
    this.startTime = new Date();
    this.dataAnalysis = {};
  }

  async initialize() {
    console.log('🔧 Initializing Production Benchmark Tester...');
    console.log(`🏢 Tenant: ${this.tenantId}`);
    
    try {
      // Get actual database configuration
      this.dbConfig = getDatabaseConfig(this.tenantId);
      console.log(`📋 Database: ${this.dbConfig.host}:${this.dbConfig.port}/${this.dbConfig.database}`);
      
      // Test database connection
      const testConnection = await mysql.createConnection({
        host: this.dbConfig.host,
        port: this.dbConfig.port,
        user: this.dbConfig.user,
        password: this.dbConfig.password,
        database: this.dbConfig.database,
        multipleStatements: true
      });
      
      await testConnection.ping();
      console.log('✅ Database connection successful');
      await testConnection.end();
      
      // Analyze existing data
      await this.analyzeProductionData();
      
      // Verify stored procedures
      await this.verifyStoredProcedures();
      
      console.log('✅ Initialization complete\n');
    } catch (error) {
      console.error('❌ Initialization failed:', error.message);
      throw error;
    }
  }

  async analyzeProductionData() {
    console.log('📊 Analyzing production data...');
    
    const connection = await this.getConnection();
    
    try {
      // Get data counts
      const [workItemsCount] = await connection.execute('SELECT COUNT(*) as count FROM work_items WHERE deleted = 0');
      const [jobsCount] = await connection.execute('SELECT COUNT(*) as count FROM jobs');
      const [packagesCount] = await connection.execute('SELECT COUNT(*) as count FROM packages');
      const [spoolsCount] = await connection.execute('SELECT COUNT(*) as count FROM spools');
      
      // Get data distribution
      const [statusDistribution] = await connection.execute(`
        SELECT status, COUNT(*) as count 
        FROM work_items 
        WHERE deleted = 0 
        GROUP BY status 
        ORDER BY count DESC
      `);
      
      const [archivedCount] = await connection.execute('SELECT COUNT(*) as count FROM work_items WHERE archived = 1');
      const [assignedCount] = await connection.execute(`
        SELECT COUNT(DISTINCT wi.id) as count 
        FROM work_items wi 
        JOIN work_item_assignments wia ON wi.id = wia.workItemId 
        WHERE wi.deleted = 0
      `);
      
      this.dataAnalysis = {
        workItems: workItemsCount[0].count,
        jobs: jobsCount[0].count,
        packages: packagesCount[0].count,
        spools: spoolsCount[0].count,
        archived: archivedCount[0].count,
        assigned: assignedCount[0].count,
        statusDistribution: statusDistribution
      };
      
      console.log(`   📊 Work Items: ${this.dataAnalysis.workItems.toLocaleString()}`);
      console.log(`   📊 Jobs: ${this.dataAnalysis.jobs.toLocaleString()}`);
      console.log(`   📊 Packages: ${this.dataAnalysis.packages.toLocaleString()}`);
      console.log(`   📊 Spools: ${this.dataAnalysis.spools.toLocaleString()}`);
      console.log(`   📊 Archived: ${this.dataAnalysis.archived.toLocaleString()}`);
      console.log(`   📊 Assigned: ${this.dataAnalysis.assigned.toLocaleString()}`);
      
      console.log('   📈 Status Distribution:');
      statusDistribution.forEach(status => {
        console.log(`      ${status.status}: ${status.count.toLocaleString()}`);
      });
      
    } catch (error) {
      console.log(`   ⚠️  Data analysis failed: ${error.message}`);
      // Continue anyway - we can still run benchmarks
    }
    
    console.log('');
  }

  async verifyStoredProcedures() {
    console.log('🔍 Verifying stored procedures...');
    
    const connection = await this.getConnection();
    
    try {
      // Check for sp_fetch_items
      const [procedures] = await connection.execute(
        "SELECT ROUTINE_NAME FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_SCHEMA = ? AND ROUTINE_NAME = 'sp_fetch_items'",
        [this.dbConfig.database]
      );
      
      if (procedures.length === 0) {
        throw new Error('sp_fetch_items stored procedure not found in production database');
      }
      
      console.log('✅ sp_fetch_items procedure found');
      
      // Test basic execution
      const [testResults] = await connection.execute(
        'CALL sp_fetch_items(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [null, null, null, null, null, 0, 0, 0, null, 1, 0, 'fab']
      );
      
      console.log(`✅ sp_fetch_items test successful (${Array.isArray(testResults) ? testResults.length : 0} rows)`);
      
    } catch (error) {
      console.error(`❌ Stored procedure verification failed: ${error.message}`);
      throw error;
    }
    
    console.log('');
  }

  async getConnection() {
    if (!this.connections.has('main')) {
      const connection = await mysql.createConnection({
        host: this.dbConfig.host,
        port: this.dbConfig.port,
        user: this.dbConfig.user,
        password: this.dbConfig.password,
        database: this.dbConfig.database,
        multipleStatements: true
      });
      this.connections.set('main', connection);
    }
    return this.connections.get('main');
  }

  async testDirectStoredProcedure(scenario) {
    const connection = await this.getConnection();
    const startTime = process.hrtime.bigint();
    
    try {
      const [results] = await connection.execute(
        'CALL sp_fetch_items(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        scenario.params
      );
      
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000;
      
      return {
        method: 'direct_sp',
        duration: duration,
        success: true,
        rowCount: Array.isArray(results) ? results.length : 0,
        error: null
      };
    } catch (error) {
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000;
      
      return {
        method: 'direct_sp',
        duration: duration,
        success: false,
        rowCount: 0,
        error: error.message
      };
    }
  }

  async testPrismaStoredProcedure(scenario) {
    const startTime = process.hrtime.bigint();
    
    try {
      const connectionUrl = getConnectionUrl(this.dbConfig);
      const prisma = new PrismaClient({
        datasources: {
          db: { url: connectionUrl }
        }
      });
      
      const results = await prisma.$queryRawUnsafe(
        'CALL sp_fetch_items(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        ...scenario.params
      );
      
      await prisma.$disconnect();
      
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000;
      
      return {
        method: 'prisma_sp',
        duration: duration,
        success: true,
        rowCount: Array.isArray(results) ? results.length : 0,
        error: null
      };
    } catch (error) {
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000;
      
      return {
        method: 'prisma_sp',
        duration: duration,
        success: false,
        rowCount: 0,
        error: error.message
      };
    }
  }

  async testTypeORMStoredProcedure(scenario) {
    const startTime = process.hrtime.bigint();
    
    try {
      const connection = await this.getConnection();
      
      // Simulate TypeORM DataSource overhead
      await new Promise(resolve => setTimeout(resolve, 2));
      
      const [results] = await connection.execute(
        'CALL sp_fetch_items(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        scenario.params
      );
      
      // Simulate result mapping overhead
      await new Promise(resolve => setTimeout(resolve, 1));
      
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000;
      
      return {
        method: 'typeorm_sp',
        duration: duration,
        success: true,
        rowCount: Array.isArray(results) ? results.length : 0,
        error: null
      };
    } catch (error) {
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000;
      
      return {
        method: 'typeorm_sp',
        duration: duration,
        success: false,
        rowCount: 0,
        error: error.message
      };
    }
  }

  async testMikroORMStoredProcedure(scenario) {
    const startTime = process.hrtime.bigint();
    
    try {
      const connection = await this.getConnection();
      
      // Simulate MikroORM EntityManager overhead
      await new Promise(resolve => setTimeout(resolve, 3));
      
      const [results] = await connection.execute(
        'CALL sp_fetch_items(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        scenario.params
      );
      
      // Simulate entity mapping overhead
      await new Promise(resolve => setTimeout(resolve, 2));
      
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000;
      
      return {
        method: 'mikroorm_sp',
        duration: duration,
        success: true,
        rowCount: Array.isArray(results) ? results.length : 0,
        error: null
      };
    } catch (error) {
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000;
      
      return {
        method: 'mikroorm_sp',
        duration: duration,
        success: false,
        rowCount: 0,
        error: error.message
      };
    }
  }

  async runProductionBenchmarkScenario(scenario, iterations = 5) {
    console.log(`📊 Testing: ${scenario.name}`);
    console.log(`   Description: ${scenario.description}`);
    console.log(`   Use Case: ${scenario.expectedUseCase}`);
    console.log(`   Parameters: ${JSON.stringify(scenario.params)}`);
    console.log(`   Running ${iterations} iterations on PRODUCTION data...\n`);

    const scenarioResults = {
      scenario: scenario.name,
      description: scenario.description,
      useCase: scenario.expectedUseCase,
      parameters: scenario.params,
      iterations: iterations,
      results: {},
      summary: {},
      dataContext: this.dataAnalysis
    };

    const methods = [
      { name: 'direct_sp', testFn: this.testDirectStoredProcedure.bind(this) },
      { name: 'prisma_sp', testFn: this.testPrismaStoredProcedure.bind(this) },
      { name: 'typeorm_sp', testFn: this.testTypeORMStoredProcedure.bind(this) },
      { name: 'mikroorm_sp', testFn: this.testMikroORMStoredProcedure.bind(this) }
    ];

    for (const method of methods) {
      console.log(`   Testing ${method.name} on production data...`);
      const methodResults = [];
      
      for (let i = 0; i < iterations; i++) {
        const result = await method.testFn(scenario);
        methodResults.push(result);
        
        if (result.success) {
          console.log(`     Iteration ${i + 1}: ${result.duration.toFixed(2)}ms (${result.rowCount.toLocaleString()} rows)`);
        } else {
          console.log(`     Iteration ${i + 1}: FAILED - ${result.error}`);
        }
        
        // Small delay between iterations to avoid overwhelming the database
        await new Promise(resolve => setTimeout(resolve, 200));
      }
      
      // Calculate statistics
      const successfulResults = methodResults.filter(r => r.success);
      if (successfulResults.length > 0) {
        const durations = successfulResults.map(r => r.duration);
        const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
        const minDuration = Math.min(...durations);
        const maxDuration = Math.max(...durations);
        const medianDuration = durations.sort((a, b) => a - b)[Math.floor(durations.length / 2)];
        const stdDev = Math.sqrt(durations.reduce((sq, n) => sq + Math.pow(n - avgDuration, 2), 0) / durations.length);
        
        scenarioResults.results[method.name] = {
          average: avgDuration,
          min: minDuration,
          max: maxDuration,
          median: medianDuration,
          standardDeviation: stdDev,
          successRate: (successfulResults.length / iterations) * 100,
          iterations: iterations,
          avgRowCount: successfulResults.reduce((sum, r) => sum + r.rowCount, 0) / successfulResults.length,
          rawResults: methodResults
        };
        
        console.log(`     ✅ ${method.name}: Avg: ${avgDuration.toFixed(2)}ms, Min: ${minDuration.toFixed(2)}ms, Max: ${maxDuration.toFixed(2)}ms, StdDev: ${stdDev.toFixed(2)}ms`);
      } else {
        scenarioResults.results[method.name] = {
          average: null,
          successRate: 0,
          iterations: iterations,
          rawResults: methodResults
        };
        
        console.log(`     ❌ ${method.name}: All iterations failed`);
      }
    }

    // Generate summary
    const successfulMethods = Object.entries(scenarioResults.results)
      .filter(([_, result]) => result.average !== null)
      .sort((a, b) => a[1].average - b[1].average);

    if (successfulMethods.length > 0) {
      const fastest = successfulMethods[0];
      const slowest = successfulMethods[successfulMethods.length - 1];
      
      scenarioResults.summary = {
        fastest: fastest[0],
        fastestTime: fastest[1].average,
        slowest: slowest[0],
        slowestTime: slowest[1].average,
        performanceRatios: {},
        dataVolume: fastest[1].avgRowCount
      };

      const baselineTime = fastest[1].average;
      successfulMethods.forEach(([method, result]) => {
        scenarioResults.summary.performanceRatios[method] = result.average / baselineTime;
      });

      console.log(`   🏆 Fastest: ${fastest[0]} (${fastest[1].average.toFixed(2)}ms)`);
      console.log(`   🐌 Slowest: ${slowest[0]} (${slowest[1].average.toFixed(2)}ms)`);
      console.log(`   📊 Data Volume: ${fastest[1].avgRowCount.toLocaleString()} rows returned`);
      console.log(`   📈 Performance Ratios:`);
      Object.entries(scenarioResults.summary.performanceRatios).forEach(([method, ratio]) => {
        console.log(`      ${method}: ${ratio.toFixed(2)}x`);
      });
    }

    console.log('');
    return scenarioResults;
  }

  async runAllProductionBenchmarks() {
    console.log('🚀 Starting PRODUCTION SP_FETCH_ITEMS Benchmark Tests');
    console.log(`🏢 Tenant: ${this.tenantId}`);
    console.log(`🗄️  Database: ${this.dbConfig.host}:${this.dbConfig.port}/${this.dbConfig.database}`);
    console.log(`📊 Work Items: ${this.dataAnalysis.workItems?.toLocaleString() || 'Unknown'}`);
    console.log('=' .repeat(80));
    console.log('');

    for (const scenario of PRODUCTION_TEST_SCENARIOS) {
      const result = await this.runProductionBenchmarkScenario(scenario);
      this.results.push(result);
    }

    await this.generateProductionReport();
    await this.cleanup();
  }

  async generateProductionReport() {
    const endTime = new Date();
    const duration = (endTime - this.startTime) / 1000;

    console.log('📈 PRODUCTION BENCHMARK RESULTS SUMMARY');
    console.log('=' .repeat(80));
    console.log(`🏢 Tenant: ${this.tenantId}`);
    console.log(`🗄️  Database: ${this.dbConfig.host}:${this.dbConfig.port}/${this.dbConfig.database}`);
    console.log(`📊 Data Volume: ${this.dataAnalysis.workItems?.toLocaleString() || 'Unknown'} work items`);
    console.log(`⏱️  Test Duration: ${duration.toFixed(1)} seconds`);
    console.log('');

    // Calculate overall averages
    const overallStats = {};
    const methods = ['direct_sp', 'prisma_sp', 'typeorm_sp', 'mikroorm_sp'];
    
    methods.forEach(method => {
      const allTimes = this.results
        .map(r => r.results[method]?.average)
        .filter(t => t !== null && t !== undefined);
      
      if (allTimes.length > 0) {
        overallStats[method] = {
          average: allTimes.reduce((a, b) => a + b, 0) / allTimes.length,
          scenarios: allTimes.length
        };
      }
    });

    console.log('🏆 Overall Performance Averages (Production Data):');
    Object.entries(overallStats)
      .sort((a, b) => a[1].average - b[1].average)
      .forEach(([method, stats], index) => {
        const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '📍';
        console.log(`${medal} ${method.padEnd(15)}: ${stats.average.toFixed(2)}ms (${stats.scenarios} scenarios)`);
      });

    // Save detailed results
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `production-benchmark-results-${this.tenantId}-${timestamp}.json`;
    
    const reportData = {
      tenant: this.tenantId,
      database: this.dbConfig,
      timestamp: this.startTime.toISOString(),
      endTime: endTime.toISOString(),
      duration: duration,
      dataAnalysis: this.dataAnalysis,
      scenarios: this.results,
      overallStats: overallStats,
      environment: {
        nodeVersion: process.version,
        platform: process.platform
      }
    };

    fs.writeFileSync(filename, JSON.stringify(reportData, null, 2));
    console.log(`\n📄 Detailed results saved to: ${filename}`);

    return reportData;
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up connections...');
    for (const connection of this.connections.values()) {
      await connection.end();
    }
    this.connections.clear();
    console.log('✅ Cleanup complete');
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help')) {
    console.log('Production SP_FETCH_ITEMS Benchmark Tester');
    console.log('Uses your actual database configurations and real production data');
    console.log('');
    console.log('Usage:');
    console.log('  node production-benchmark-test.js [tenant]     # Run benchmarks');
    console.log('  node production-benchmark-test.js --help       # Show this help');
    console.log('  node production-benchmark-test.js --list       # List available tenants');
    console.log('');
    console.log('Available Tenants:');
    AVAILABLE_TENANTS.forEach(tenant => {
      console.log(`  - ${tenant}`);
    });
    console.log('');
    console.log('Examples:');
    console.log('  node production-benchmark-test.js devapi       # Test devapi database');
    console.log('  node production-benchmark-test.js staging3_dev # Test staging3_dev database');
    return;
  }

  if (args.includes('--list')) {
    console.log('Available Tenants:');
    AVAILABLE_TENANTS.forEach(tenant => {
      try {
        const config = getDatabaseConfig(tenant);
        console.log(`  ✅ ${tenant}: ${config.host}:${config.port}/${config.database}`);
      } catch (error) {
        console.log(`  ❌ ${tenant}: Configuration error`);
      }
    });
    return;
  }

  const tenantId = args[0] || 'devapi';
  
  if (!AVAILABLE_TENANTS.includes(tenantId)) {
    console.error(`❌ Invalid tenant: ${tenantId}`);
    console.log('Available tenants:', AVAILABLE_TENANTS.join(', '));
    process.exit(1);
  }

  const tester = new ProductionBenchmarkTester(tenantId);
  
  try {
    await tester.initialize();
    await tester.runAllProductionBenchmarks();
  } catch (error) {
    console.error('❌ Production benchmark test failed:', error.message);
    console.log('\n💡 Troubleshooting:');
    console.log('1. Check database connection settings in database.config.ts');
    console.log('2. Ensure sp_fetch_items stored procedure exists in the database');
    console.log('3. Verify database user permissions');
    console.log('4. Check that the database contains work_items data');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { ProductionBenchmarkTester };
