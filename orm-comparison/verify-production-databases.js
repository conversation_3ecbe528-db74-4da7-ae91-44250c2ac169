#!/usr/bin/env node

/**
 * Production Database Verification Script
 * Verifies connections to actual databases and checks for required data/procedures
 */

const mysql = require('mysql2/promise');
const fs = require('fs');

class ProductionDatabaseVerifier {
  constructor() {
    this.results = {};
  }

  async verifyTenant(tenantId) {
    console.log(`🔍 Verifying tenant: ${tenantId}`);
    
    try {
      // Build TypeScript first to get config
      if (!fs.existsSync('./dist/config/database.config.js')) {
        console.log('   📦 Building TypeScript configuration...');
        const { spawn } = require('child_process');
        await new Promise((resolve, reject) => {
          const build = spawn('npm', ['run', 'build'], { stdio: 'pipe' });
          build.on('close', (code) => code === 0 ? resolve() : reject(new Error('Build failed')));
        });
      }

      const { getDatabaseConfig } = require('./dist/config/database.config');
      const config = getDatabaseConfig(tenantId);
      
      console.log(`   📋 Config: ${config.host}:${config.port}/${config.database}`);
      
      // Test connection
      const connection = await mysql.createConnection({
        host: config.host,
        port: config.port,
        user: config.user,
        password: config.password,
        database: config.database,
        connectTimeout: 10000
      });
      
      console.log('   ✅ Database connection successful');
      
      // Check data
      const dataChecks = await this.checkData(connection);
      
      // Check stored procedures
      const procedureChecks = await this.checkStoredProcedures(connection, config.database);
      
      // Test sp_fetch_items
      const spTest = await this.testSpFetchItems(connection);
      
      await connection.end();
      
      this.results[tenantId] = {
        config: config,
        connection: true,
        data: dataChecks,
        procedures: procedureChecks,
        spTest: spTest,
        status: 'success'
      };
      
      console.log(`   ✅ ${tenantId} verification complete\n`);
      
    } catch (error) {
      console.log(`   ❌ ${tenantId} verification failed: ${error.message}\n`);
      this.results[tenantId] = {
        connection: false,
        error: error.message,
        status: 'failed'
      };
    }
  }

  async checkData(connection) {
    console.log('   📊 Checking data...');
    
    const checks = {};
    
    try {
      // Check main tables
      const tables = ['work_items', 'jobs', 'packages', 'spools'];
      
      for (const table of tables) {
        try {
          const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
          checks[table] = rows[0].count;
          console.log(`      ${table}: ${rows[0].count.toLocaleString()} rows`);
        } catch (error) {
          checks[table] = `Error: ${error.message}`;
          console.log(`      ${table}: ❌ ${error.message}`);
        }
      }
      
      // Check work_items details
      if (checks.work_items && typeof checks.work_items === 'number' && checks.work_items > 0) {
        const [activeItems] = await connection.execute('SELECT COUNT(*) as count FROM work_items WHERE deleted = 0');
        const [archivedItems] = await connection.execute('SELECT COUNT(*) as count FROM work_items WHERE archived = 1');
        
        checks.work_items_active = activeItems[0].count;
        checks.work_items_archived = archivedItems[0].count;
        
        console.log(`      work_items (active): ${activeItems[0].count.toLocaleString()}`);
        console.log(`      work_items (archived): ${archivedItems[0].count.toLocaleString()}`);
      }
      
    } catch (error) {
      console.log(`      ❌ Data check error: ${error.message}`);
    }
    
    return checks;
  }

  async checkStoredProcedures(connection, database) {
    console.log('   🔧 Checking stored procedures...');
    
    const checks = {};
    
    try {
      // Check for sp_fetch_items
      const [spFetchItems] = await connection.execute(
        "SELECT ROUTINE_NAME FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_SCHEMA = ? AND ROUTINE_NAME = 'sp_fetch_items'",
        [database]
      );
      
      checks.sp_fetch_items = spFetchItems.length > 0;
      console.log(`      sp_fetch_items: ${checks.sp_fetch_items ? '✅ Found' : '❌ Not found'}`);
      
      // Check for basic CRUD procedures
      const basicProcedures = ['create_user', 'get_users', 'get_user_by_id', 'update_user', 'delete_user'];
      
      for (const procName of basicProcedures) {
        const [proc] = await connection.execute(
          "SELECT ROUTINE_NAME FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_SCHEMA = ? AND ROUTINE_NAME = ?",
          [database, procName]
        );
        
        checks[procName] = proc.length > 0;
        console.log(`      ${procName}: ${checks[procName] ? '✅ Found' : '⚠️  Not found (optional)'}`);
      }
      
    } catch (error) {
      console.log(`      ❌ Procedure check error: ${error.message}`);
    }
    
    return checks;
  }

  async testSpFetchItems(connection) {
    console.log('   🧪 Testing sp_fetch_items...');
    
    try {
      const startTime = process.hrtime.bigint();
      
      const [results] = await connection.execute(
        'CALL sp_fetch_items(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [null, null, null, null, null, 0, 0, 0, null, 1, 0, 'fab']
      );
      
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000;
      
      const rowCount = Array.isArray(results) ? results.length : 0;
      
      console.log(`      ✅ sp_fetch_items test successful: ${duration.toFixed(2)}ms, ${rowCount.toLocaleString()} rows`);
      
      return {
        success: true,
        duration: duration,
        rowCount: rowCount
      };
      
    } catch (error) {
      console.log(`      ❌ sp_fetch_items test failed: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async verifyAllTenants() {
    console.log('🚀 Production Database Verification');
    console.log('=' .repeat(50));
    console.log('');

    const tenants = ['devapi', 'staging3_dev'];
    
    for (const tenant of tenants) {
      await this.verifyTenant(tenant);
    }
    
    this.generateSummary();
  }

  generateSummary() {
    console.log('📋 VERIFICATION SUMMARY');
    console.log('=' .repeat(50));
    
    const successful = Object.keys(this.results).filter(t => this.results[t].status === 'success');
    const failed = Object.keys(this.results).filter(t => this.results[t].status === 'failed');
    
    console.log(`✅ Successful: ${successful.length}`);
    console.log(`❌ Failed: ${failed.length}`);
    console.log('');
    
    // Detailed results
    Object.entries(this.results).forEach(([tenant, result]) => {
      console.log(`🏢 ${tenant}:`);
      
      if (result.status === 'success') {
        console.log(`   ✅ Connection: ${result.config.host}:${result.config.port}/${result.config.database}`);
        console.log(`   📊 Work Items: ${result.data.work_items?.toLocaleString() || 'Unknown'}`);
        console.log(`   🔧 sp_fetch_items: ${result.procedures.sp_fetch_items ? 'Available' : 'Missing'}`);
        
        if (result.spTest.success) {
          console.log(`   🧪 Test Result: ${result.spTest.duration.toFixed(2)}ms, ${result.spTest.rowCount.toLocaleString()} rows`);
        } else {
          console.log(`   🧪 Test Result: Failed - ${result.spTest.error}`);
        }
      } else {
        console.log(`   ❌ Error: ${result.error}`);
      }
      
      console.log('');
    });
    
    // Recommendations
    console.log('💡 RECOMMENDATIONS:');
    
    if (successful.length > 0) {
      console.log(`✅ Ready for benchmarking on: ${successful.join(', ')}`);
      console.log('   Run: node run-production-benchmarks.js [tenant]');
    }
    
    if (failed.length > 0) {
      console.log(`⚠️  Fix issues for: ${failed.join(', ')}`);
      console.log('   Check database connectivity and credentials');
    }
    
    // Check for missing procedures
    const missingProcedures = Object.values(this.results)
      .filter(r => r.status === 'success' && !r.procedures.sp_fetch_items)
      .length;
    
    if (missingProcedures > 0) {
      console.log('⚠️  Install sp_fetch_items stored procedure:');
      console.log('   mysql -u user -p database < sp_fetch_items.sql');
    }
    
    console.log('');
    
    // Save results
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `database-verification-${timestamp}.json`;
    fs.writeFileSync(filename, JSON.stringify(this.results, null, 2));
    console.log(`📄 Verification results saved to: ${filename}`);
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help')) {
    console.log('Production Database Verification Script');
    console.log('');
    console.log('This script verifies:');
    console.log('  - Database connectivity');
    console.log('  - Data availability');
    console.log('  - Stored procedure existence');
    console.log('  - sp_fetch_items functionality');
    console.log('');
    console.log('Usage:');
    console.log('  node verify-production-databases.js        # Verify all tenants');
    console.log('  node verify-production-databases.js --help # Show this help');
    return;
  }

  const verifier = new ProductionDatabaseVerifier();
  
  try {
    await verifier.verifyAllTenants();
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { ProductionDatabaseVerifier };
