export interface DatabaseConfig {
  database: string;
  userPattern: string;
  port: number;
  host: string;
  user: string;
  password: string;
}

export const databaseConfigs: { [key: string]: DatabaseConfig } = {
  devapi: {
    database: 'devapi',
    userPattern: 'devapi',
    port: 65107,
    host: 'localhost',
    user: 'root',
    password: 'admin',
  },
  staging3_dev: {
    database: 'staging3',
    userPattern: 'staging3_dev',
    port: 65107,
    host: 'localhost',
    user: 'root',
    password: 'admin',
  },
  accoc_staging:{
    database:'accoc_staging',
    userPattern:'accoc_staging',
    port: 65107,
    host: 'localhost',
    user: 'root',
    password: 'admin',
  }
};

export function getDatabaseConfig(tenantId: string): DatabaseConfig {
  const config = databaseConfigs[tenantId];
  if (!config) {
    throw new Error(`No database configuration found for tenant ${tenantId}`);
  }
  return config;
}

export function getConnectionUrl(config: DatabaseConfig): string {
  return `mysql://${config.user}:${config.password}@${config.host}:${config.port}/${config.database}`;
}
