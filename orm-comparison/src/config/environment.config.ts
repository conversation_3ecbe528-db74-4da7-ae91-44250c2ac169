import { getDatabaseConfig, getConnectionUrl } from './database.config';

export class EnvironmentConfig {
  static readonly PORT = process.env.PORT || 3000;
  static readonly NODE_ENV = process.env.NODE_ENV || 'development';
  static readonly TENANT_HEADER = 'X-Tenant-Id';

  // Generate database URL for a specific client
  static getDatabaseUrl(clientId: string): string {
    const config = getDatabaseConfig(clientId);
    return getConnectionUrl(config);
  }

  static getDatabaseConfig(clientId: string) {
    return getDatabaseConfig(clientId);
  }
}
