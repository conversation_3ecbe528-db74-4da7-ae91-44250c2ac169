import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Req,
} from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { CreateUserDto, UpdateUserDto } from '../../shared/interfaces/user.dto';
import { Request } from 'express';
import { Measure } from '../../common/decorators/performance.decorator';

@Controller('prisma/users')
export class PrismaUserController {
  constructor(private readonly prismaService: PrismaService) {}

  @Post()
  @Measure()
  async create(@Body() createUserDto: CreateUserDto, @Req() req: Request) {
    const prisma = this.prismaService.setClientDatabase(req);
    return prisma.user.create({
      data: createUserDto,
    });
  }

  @Get()
  @Measure()
  async findAll(@Req() req: Request) {
    const prisma = this.prismaService.setClientDatabase(req);
    return prisma.user.findMany();
  }

  @Get(':id')
  @Measure()
  async findOne(@Param('id') id: string, @Req() req: Request) {
    const prisma = this.prismaService.setClientDatabase(req);
    return prisma.user.findUnique({
      where: { id: parseInt(id) },
    });
  }

  @Put(':id')
  @Measure()
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @Req() req: Request,
  ) {
    const prisma = this.prismaService.setClientDatabase(req);
    return prisma.user.update({
      where: { id: parseInt(id) },
      data: updateUserDto,
    });
  }

  @Delete(':id')
  @Measure()
  async remove(@Param('id') id: string, @Req() req: Request) {
    const prisma = this.prismaService.setClientDatabase(req);
    return prisma.user.delete({
      where: { id: parseInt(id) },
    });
  }

  @Post('benchmark')
  @Measure()
  async benchmark(@Req() req: Request) {
    const prisma = this.prismaService.setClientDatabase(req);
    const startTime = process.hrtime();

    // Create 100 users
    const users = await Promise.all(
      Array(100)
        .fill(null)
        .map((_, index) =>
          prisma.user.create({
            data: {
              name: `User ${index}`,
              email: `user${index}@example.com`,
              status: true,
            },
          }),
        ),
    );

    // Read all users
    await prisma.user.findMany();

    // Update all users
    await Promise.all(
      users.map((user) =>
        prisma.user.update({
          where: { id: user.id },
          data: { status: false },
        }),
      ),
    );

    // Delete all users
    await prisma.user.deleteMany({});

    const endTime = process.hrtime(startTime);
    const duration = (endTime[0] * 1000000000 + endTime[1]) / 1000000; // Convert to milliseconds

    return { duration, operation: 'benchmark', orm: 'prisma' };
  }

  // Stored procedure endpoints
  @Post('sp/users')
  @Measure()
  async createUserSP(@Body() createUserDto: CreateUserDto, @Req() req: Request) {
    return this.prismaService.createUserSP(req, createUserDto.name, createUserDto.email, createUserDto.status ?? true);
  }

  @Get('sp/users')
  @Measure()
  async getUsersSP(@Req() req: Request) {
    return this.prismaService.getUsersSP(req);
  }

  @Get('sp/users/:id')
  @Measure()
  async getUserByIdSP(@Param('id') id: string, @Req() req: Request) {
    return this.prismaService.getUserByIdSP(req, parseInt(id));
  }

  @Put('sp/users/:id')
  @Measure()
  async updateUserSP(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @Req() req: Request,
  ) {
    return this.prismaService.updateUserSP(req, parseInt(id), updateUserDto.name, updateUserDto.email, updateUserDto.status);
  }

  @Delete('sp/users/:id')
  @Measure()
  async deleteUserSP(@Param('id') id: string, @Req() req: Request) {
    return this.prismaService.deleteUserSP(req, parseInt(id));
  }

  @Post('sp/fetch-items')
  @Measure()
  async fetchItemsSP(@Body() params: any, @Req() req: Request) {
    return this.prismaService.fetchItemsSP(req, params);
  }
}
