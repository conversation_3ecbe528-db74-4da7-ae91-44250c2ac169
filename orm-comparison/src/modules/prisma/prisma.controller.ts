import { Controller, Get, Post, Put, Delete, Body, Param, Req } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { CreateUserDto, UpdateUserDto } from '../../shared/interfaces/user.dto';
import { Request } from 'express';
import { Measure } from '../../common/decorators/performance.decorator';

@Controller('prisma/users')
export class PrismaUserController {
  constructor(private readonly prismaService: PrismaService) {}

  @Post()
  @Measure()
  async create(@Body() createUserDto: CreateUserDto, @Req() req: Request) {
    this.prismaService.setClientDatabase(req);
    return this.prismaService.user.create({
      data: createUserDto,
    });
  }

  @Get()
  @Measure()
  async findAll(@Req() req: Request) {
    this.prismaService.setClientDatabase(req);
    return this.prismaService.user.findMany();
  }

  @Get(':id')
  @Measure()
  async findOne(@Param('id') id: string, @Req() req: Request) {
    this.prismaService.setClientDatabase(req);
    return this.prismaService.user.findUnique({
      where: { id: parseInt(id) },
    });
  }

  @Put(':id')
  @Measure()
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @Req() req: Request,
  ) {
    this.prismaService.setClientDatabase(req);
    return this.prismaService.user.update({
      where: { id: parseInt(id) },
      data: updateUserDto,
    });
  }

  @Delete(':id')
  @Measure()
  async remove(@Param('id') id: string, @Req() req: Request) {
    this.prismaService.setClientDatabase(req);
    return this.prismaService.user.delete({
      where: { id: parseInt(id) },
    });
  }

  @Post('benchmark')
  @Measure()
  async benchmark(@Req() req: Request) {
    this.prismaService.setClientDatabase(req);
    const startTime = process.hrtime();

    // Create 100 users
    const users = await Promise.all(
      Array(100)
        .fill(null)
        .map((_, index) =>
          this.prismaService.user.create({
            data: {
              name: `User ${index}`,
              email: `user${index}@example.com`,
              status: true,
            },
          }),
        ),
    );

    // Read all users
    await this.prismaService.user.findMany();

    // Update all users
    await Promise.all(
      users.map((user) =>
        this.prismaService.user.update({
          where: { id: user.id },
          data: { status: false },
        }),
      ),
    );

    // Delete all users
    await this.prismaService.user.deleteMany({});

    const endTime = process.hrtime(startTime);
    const duration = (endTime[0] * 1000000000 + endTime[1]) / 1000000; // Convert to milliseconds

    return { duration, operation: 'benchmark', orm: 'prisma' };
  }
}
