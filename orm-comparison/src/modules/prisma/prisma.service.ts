import { Injectable, OnModuleInit, OnModuleD<PERSON>roy, BadRequestEx<PERSON> } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { Request } from 'express';
import { EnvironmentConfig } from '../../../config/environment.config';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  constructor() {
    super();
  }

  async onModuleInit() {
    // We're using per-tenant PrismaClient instances, so no need to connect here
  }

  private clientPrismas: Map<string, PrismaClient> = new Map();

  // Method to set the client database connection for multi-tenant
  setClientDatabase(req: Request) {
    const clientId = (req as any).tenantId;
    if (!clientId) {
      throw new BadRequestException('Client ID is required');
    }

    if (!this.clientPrismas.has(clientId)) {
      const dbConfig = EnvironmentConfig.getDatabaseConfig(clientId);
      const url = `mysql://${dbConfig.user}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`;
      const prisma = new PrismaClient({
        datasources: {
          db: {
            url: url,
          },
        },
      });
      this.clientPrismas.set(clientId, prisma);
    }
    return this.clientPrismas.get(clientId)!;
  }

  async onModuleDestroy() {
    await Promise.all(
      Array.from(this.clientPrismas.values()).map((prisma) => prisma.$disconnect())
    );
    this.clientPrismas.clear();
  }
}
