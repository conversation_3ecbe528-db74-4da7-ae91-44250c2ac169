import { Injectable, OnModuleInit, OnM<PERSON>ule<PERSON><PERSON>roy, BadRequestEx<PERSON> } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { Request } from 'express';
import { EnvironmentConfig } from '../../config/environment.config';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  constructor() {
    super();
  }

  async onModuleInit() {
    // We're using per-tenant PrismaClient instances, so no need to connect here
  }

  private clientPrismas: Map<string, PrismaClient> = new Map();

  // Method to set the client database connection for multi-tenant
  setClientDatabase(req: Request) {
    const clientId = (req as any).tenantId;
    if (!clientId) {
      throw new BadRequestException('Client ID is required');
    }

    if (!this.clientPrismas.has(clientId)) {
      const dbConfig = EnvironmentConfig.getDatabaseConfig(clientId);
      const url = `mysql://${dbConfig.user}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`;
      const prisma = new PrismaClient({
        datasources: {
          db: {
            url: url,
          },
        },
      });
      this.clientPrismas.set(clientId, prisma);
    }
    return this.clientPrismas.get(clientId)!;
  }

  // Stored procedure methods
  async callStoredProcedure(req: Request, procedureName: string, params: any[] = []): Promise<any> {
    const clientId = (req as any).tenantId;
    if (!clientId) {
      throw new BadRequestException('Client ID is required');
    }

    const prisma = this.setClientDatabase(req);
    const startTime = process.hrtime();

    try {
      const placeholders = params.map(() => '?').join(', ');
      const query = `CALL ${procedureName}(${placeholders})`;
      const result = await prisma.$queryRawUnsafe(query, ...params);

      const endTime = process.hrtime(startTime);
      const duration = (endTime[0] * 1000000000 + endTime[1]) / 1000000;

      return {
        duration,
        operation: procedureName,
        method: 'prisma_stored_procedure',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException(`Prisma stored procedure error: ${error.message}`);
    }
  }

  async createUserSP(req: Request, name: string, email: string, status: boolean = true): Promise<any> {
    return this.callStoredProcedure(req, 'create_user', [name, email, status]);
  }

  async getUsersSP(req: Request): Promise<any> {
    return this.callStoredProcedure(req, 'get_users');
  }

  async getUserByIdSP(req: Request, id: number): Promise<any> {
    return this.callStoredProcedure(req, 'get_user_by_id', [id]);
  }

  async updateUserSP(req: Request, id: number, name?: string, email?: string, status?: boolean): Promise<any> {
    return this.callStoredProcedure(req, 'update_user', [id, name, email, status]);
  }

  async deleteUserSP(req: Request, id: number): Promise<any> {
    return this.callStoredProcedure(req, 'delete_user', [id]);
  }

  async benchmarkOperationsSP(req: Request): Promise<any> {
    return this.callStoredProcedure(req, 'benchmark_operations');
  }

  async onModuleDestroy() {
    await Promise.all(
      Array.from(this.clientPrismas.values()).map((prisma) => prisma.$disconnect())
    );
    this.clientPrismas.clear();
  }
}
