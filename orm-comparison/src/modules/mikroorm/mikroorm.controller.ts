import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Req,
} from '@nestjs/common';
import { MikroOrmService } from './mikroorm.service';
import { CreateUserDto, UpdateUserDto } from '../../shared/interfaces/user.dto';
import { Request } from 'express';
import { Measure } from '../../common/decorators/performance.decorator';

@Controller('mikroorm/users')
export class MikroOrmUserController {
  constructor(private readonly mikroOrmService: MikroOrmService) {}

  @Post()
  @Measure()
  async create(@Body() createUserDto: CreateUserDto, @Req() req: Request) {
    return this.mikroOrmService.createUserSP(req, createUserDto.name, createUserDto.email, createUserDto.status ?? true);
  }

  @Get()
  @Measure()
  async findAll(@Req() req: Request) {
    return this.mikroOrmService.getUsersSP(req);
  }

  @Get(':id')
  @Measure()
  async findOne(@Param('id') id: string, @Req() req: Request) {
    return this.mikroOrmService.getUserByIdSP(req, parseInt(id));
  }

  @Put(':id')
  @Measure()
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @Req() req: Request,
  ) {
    return this.mikroOrmService.updateUserSP(req, parseInt(id), updateUserDto.name, updateUserDto.email, updateUserDto.status);
  }

  @Delete(':id')
  @Measure()
  async remove(@Param('id') id: string, @Req() req: Request) {
    return this.mikroOrmService.deleteUserSP(req, parseInt(id));
  }

  @Post('benchmark')
  @Measure()
  async benchmark(@Req() req: Request) {
    return this.mikroOrmService.benchmarkOperationsSP(req);
  }

  // Stored procedure endpoints
  @Post('sp/users')
  @Measure()
  async createUserSP(@Body() createUserDto: CreateUserDto, @Req() req: Request) {
    return this.mikroOrmService.createUserSP(req, createUserDto.name, createUserDto.email, createUserDto.status ?? true);
  }

  @Get('sp/users')
  @Measure()
  async getUsersSP(@Req() req: Request) {
    return this.mikroOrmService.getUsersSP(req);
  }

  @Get('sp/users/:id')
  @Measure()
  async getUserByIdSP(@Param('id') id: string, @Req() req: Request) {
    return this.mikroOrmService.getUserByIdSP(req, parseInt(id));
  }

  @Put('sp/users/:id')
  @Measure()
  async updateUserSP(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @Req() req: Request,
  ) {
    return this.mikroOrmService.updateUserSP(req, parseInt(id), updateUserDto.name, updateUserDto.email, updateUserDto.status);
  }

  @Delete('sp/users/:id')
  @Measure()
  async deleteUserSP(@Param('id') id: string, @Req() req: Request) {
    return this.mikroOrmService.deleteUserSP(req, parseInt(id));
  }

  @Post('sp/benchmark')
  @Measure()
  async benchmarkSP(@Req() req: Request) {
    return this.mikroOrmService.benchmarkOperationsSP(req);
  }
}
