import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Req,
} from '@nestjs/common';
import { MikroOrmService } from './mikroorm.service';
import { CreateUserDto, UpdateUserDto } from '../../shared/interfaces/user.dto';
import { Request } from 'express';
import { Measure } from '../../common/decorators/performance.decorator';

@Controller('mikroorm/users')
export class MikroOrmUserController {
  constructor(private readonly mikroOrmService: MikroOrmService) {}

  @Post()
  @Measure()
  async create(@Body() createUserDto: CreateUserDto, @Req() req: Request) {
    const repo = await this.mikroOrmService.getUserRepository(req);
    const orm = await this.mikroOrmService.getClientOrm((req as any).tenantId);
    const user = repo.create({
      ...createUserDto,
      status: createUserDto.status ?? true,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    await orm.em.persistAndFlush(user);
    return user;
  }

  @Get()
  @Measure()
  async findAll(@Req() req: Request) {
    const repo = await this.mikroOrmService.getUserRepository(req);
    return repo.findAll();
  }

  @Get(':id')
  @Measure()
  async findOne(@Param('id') id: string, @Req() req: Request) {
    const repo = await this.mikroOrmService.getUserRepository(req);
    return repo.findOne({ id: parseInt(id) });
  }

  @Put(':id')
  @Measure()
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @Req() req: Request,
  ) {
    const repo = await this.mikroOrmService.getUserRepository(req);
    const orm = await this.mikroOrmService.getClientOrm((req as any).tenantId);
    const user = await repo.findOne({ id: parseInt(id) });
    if (!user) return null;
    repo.assign(user, updateUserDto);
    await orm.em.persistAndFlush(user);
    return user;
  }

  @Delete(':id')
  @Measure()
  async remove(@Param('id') id: string, @Req() req: Request) {
    const repo = await this.mikroOrmService.getUserRepository(req);
    const orm = await this.mikroOrmService.getClientOrm((req as any).tenantId);
    const user = await repo.findOne({ id: parseInt(id) });
    if (!user) return null;
    await orm.em.removeAndFlush(user);
    return user;
  }

  @Post('benchmark')
  @Measure()
  async benchmark(@Req() req: Request) {
    const repo = await this.mikroOrmService.getUserRepository(req);
    const orm = await this.mikroOrmService.getClientOrm((req as any).tenantId);
    const startTime = process.hrtime();
    // Create 100 users
    const users = [];
    for (let i = 0; i < 100; i++) {
      const user = repo.create({
        name: `User ${i}`,
        email: `user${i}@example.com`,
        status: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
      users.push(user);
    }
    await orm.em.persistAndFlush(users);
    // Read all users
    await repo.findAll();
    // Update all users
    for (const user of users) {
      user.status = false;
    }
    await orm.em.persistAndFlush(users);
    // Delete all users
    for (const user of users) {
      await orm.em.removeAndFlush(user);
    }
    const endTime = process.hrtime(startTime);
    const duration = (endTime[0] * 1000000000 + endTime[1]) / 1000000;
    return { duration, operation: 'benchmark', orm: 'mikroorm' };
  }

  // Stored procedure endpoints
  @Post('sp/users')
  @Measure()
  async createUserSP(@Body() createUserDto: CreateUserDto, @Req() req: Request) {
    return this.mikroOrmService.createUserSP(req, createUserDto.name, createUserDto.email, createUserDto.status ?? true);
  }

  @Get('sp/users')
  @Measure()
  async getUsersSP(@Req() req: Request) {
    return this.mikroOrmService.getUsersSP(req);
  }

  @Get('sp/users/:id')
  @Measure()
  async getUserByIdSP(@Param('id') id: string, @Req() req: Request) {
    return this.mikroOrmService.getUserByIdSP(req, parseInt(id));
  }

  @Put('sp/users/:id')
  @Measure()
  async updateUserSP(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @Req() req: Request,
  ) {
    return this.mikroOrmService.updateUserSP(req, parseInt(id), updateUserDto.name, updateUserDto.email, updateUserDto.status);
  }

  @Delete('sp/users/:id')
  @Measure()
  async deleteUserSP(@Param('id') id: string, @Req() req: Request) {
    return this.mikroOrmService.deleteUserSP(req, parseInt(id));
  }

  @Post('sp/benchmark')
  @Measure()
  async benchmarkSP(@Req() req: Request) {
    return this.mikroOrmService.benchmarkOperationsSP(req);
  }
}
