import {
  Injectable,
  BadRequestException,
  OnApplicationShutdown,
} from '@nestjs/common';
import { MikroORM } from '@mikro-orm/core';
import { Request } from 'express';
import { getDatabaseConfig } from '../../config/database.config';

@Injectable()
export class MikroOrmService implements OnApplicationShutdown {
  private clientOrms: Map<string, MikroORM> = new Map();

  constructor() {}

  async getClientOrm(clientId: string): Promise<MikroORM> {
    if (!this.clientOrms.has(clientId)) {
      const dbConfig = getDatabaseConfig(clientId);
      const { MySqlDriver } = await import('@mikro-orm/mysql');
      const orm = await MikroORM.init({
        entities: [], // No entities to prevent schema operations
        dbName: dbConfig.database,
        driver: MySqlDriver, // Use MySQL driver (compatible with MariaDB)
        host: dbConfig.host,
        port: dbConfig.port,
        user: dbConfig.user,
        password: dbConfig.password,
        debug: false, // Disable debug logging
      });
      this.clientOrms.set(clientId, orm);
    }
    return this.clientOrms.get(clientId)!;
  }

  // Raw query method for MikroORM without entities
  async executeRawQuery(req: Request, query: string, params: any[] = []): Promise<any> {
    const clientId = (req as any).tenantId;
    if (!clientId) throw new BadRequestException('Client ID is required');

    const orm = await this.getClientOrm(clientId);
    const startTime = process.hrtime();

    try {
      const result = await orm.em.getConnection().execute(query, params);

      const endTime = process.hrtime(startTime);
      const duration = (endTime[0] * 1000000000 + endTime[1]) / 1000000;

      return {
        duration,
        method: 'mikroorm_raw_query',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException(`MikroORM raw query error: ${error.message}`);
    }
  }

  // Stored procedure methods
  async callStoredProcedure(req: Request, procedureName: string, params: any[] = []): Promise<any> {
    const clientId = (req as any).tenantId;
    if (!clientId) throw new BadRequestException('Client ID is required');

    const orm = await this.getClientOrm(clientId);
    const startTime = process.hrtime();

    try {
      const placeholders = params.map(() => '?').join(', ');
      const query = `CALL ${procedureName}(${placeholders})`;
      const result = await orm.em.getConnection().execute(query, params);

      const endTime = process.hrtime(startTime);
      const duration = (endTime[0] * 1000000000 + endTime[1]) / 1000000;

      return {
        duration,
        operation: procedureName,
        method: 'mikroorm_stored_procedure',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException(`MikroORM stored procedure error: ${error.message}`);
    }
  }

  async createUserSP(req: Request, name: string, email: string, status: boolean = true): Promise<any> {
    return this.callStoredProcedure(req, 'create_user', [name, email, status]);
  }

  async getUsersSP(req: Request): Promise<any> {
    return this.callStoredProcedure(req, 'get_users');
  }

  async getUserByIdSP(req: Request, id: number): Promise<any> {
    return this.callStoredProcedure(req, 'get_user_by_id', [id]);
  }

  async updateUserSP(req: Request, id: number, name?: string, email?: string, status?: boolean): Promise<any> {
    return this.callStoredProcedure(req, 'update_user', [id, name, email, status]);
  }

  async deleteUserSP(req: Request, id: number): Promise<any> {
    return this.callStoredProcedure(req, 'delete_user', [id]);
  }

  async benchmarkOperationsSP(req: Request): Promise<any> {
    return this.callStoredProcedure(req, 'benchmark_operations');
  }

  async onApplicationShutdown() {
    await Promise.all(
      Array.from(this.clientOrms.values()).map((orm) => orm.close(true)),
    );
    this.clientOrms.clear();
  }
}
