import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ey, Property } from '@mikro-orm/core';
import { IWorkItems } from '../../../shared/interfaces/work_items.interface';

@Entity({ tableName: 'work_items' })
export class WorkItems implements IWorkItems {
  @PrimaryKey()
  id!: number;

  @Property()
  name!: string;

  @Property()
  spoolId!: number;

  @Property()
  status!: string;

  @Property({ onCreate: () => new Date() })
  createdAt: Date = new Date();

  @Property({ onUpdate: () => new Date() })
  updatedAt: Date = new Date();
}
