import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ey, Property } from '@mikro-orm/core';
import { ISpools } from '../../../shared/interfaces/spools.interface';

@Entity({ tableName: 'spools' })
export class Spools implements ISpools {
  @PrimaryKey()
  id!: number;

  @Property()
  name!: string;

  @Property()
  packageId!: number;

  @Property()
  status!: string;

  @Property({ onCreate: () => new Date() })
  createdAt: Date = new Date();

  @Property({ onUpdate: () => new Date() })
  updatedAt: Date = new Date();
}
