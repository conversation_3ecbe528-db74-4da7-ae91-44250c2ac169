import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ey, Property } from '@mikro-orm/core';
import { IJobs } from '../../../shared/interfaces/jobs.interface';

@Entity({ tableName: 'jobs' })
export class Jobs implements IJobs {
  @PrimaryKey()
  id!: number;

  @Property()
  name!: string;

  @Property()
  status!: string;

  @Property({ onCreate: () => new Date() })
  createdAt: Date = new Date();

  @Property({ onUpdate: () => new Date() })
  updatedAt: Date = new Date();
}
