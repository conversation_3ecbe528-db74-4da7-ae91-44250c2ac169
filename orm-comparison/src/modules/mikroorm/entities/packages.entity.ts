import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ey, Property } from '@mikro-orm/core';
import { IPackages } from '../../../shared/interfaces/packages.interface';

@Entity({ tableName: 'packages' })
export class Packages implements IPackages {
  @PrimaryKey()
  id!: number;

  @Property()
  name!: string;

  @Property()
  jobId!: number;

  @Property()
  status!: string;

  @Property({ onCreate: () => new Date() })
  createdAt: Date = new Date();

  @Property({ onUpdate: () => new Date() })
  updatedAt: Date = new Date();
}
