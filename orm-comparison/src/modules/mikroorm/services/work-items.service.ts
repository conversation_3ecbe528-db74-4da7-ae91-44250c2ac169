import { Injectable, BadRequestException } from '@nestjs/common';
import { WorkItems } from '../entities/work_items.entity';
import { MikroOrmService } from '../mikroorm.service';
import { Request } from 'express';

export interface FetchItemsOptions {
  workItemColumnIds?: string[];
  jobIds?: number[];
  packageIds?: number[];
  drawingIds?: number[];
  stageIds?: number[];
  bom?: boolean;
  isAssigned?: boolean;
  includeArchived?: boolean;
  currentUser?: number;
  defaultRound?: number;
  groupHow?: 0 | 1 | 2 | 3;
  appType?: 'fab';
}

@Injectable()
export class WorkItemsMikroService {
  constructor(private readonly mikroOrmService: MikroOrmService) {}

  async fetchItems(req: Request, options: FetchItemsOptions) {
    const tenantId = (req as any).tenantId;
    if (!tenantId) {
      throw new BadRequestException('Tenant ID is required');
    }
    const orm = await this.mikroOrmService.getClientOrm(tenantId);
    const repository = orm.em.getRepository(WorkItems);

    // For now, return a simple find operation
    // The complex query builder functionality would need proper MikroORM setup
    return repository.findAll();
  }

  private applyFilters(qb: any, options: FetchItemsOptions) {
    const {
      jobIds,
      packageIds,
      drawingIds,
      stageIds,
      bom,
      includeArchived,
      appType = 'fab',
    } = options;

    if (jobIds?.length) {
      qb.andWhere({ jobId: { $in: jobIds } });
    }

    if (packageIds?.length) {
      qb.andWhere({ packageId: { $in: packageIds } });
    }

    if (drawingIds?.length) {
      qb.andWhere({ spoolId: { $in: drawingIds } });
    }

    if (stageIds?.length) {
      qb.leftJoinAndSelect('wi.stages', 'stages').andWhere({
        'stages.stageId': { $in: stageIds },
      });
    }

    if (bom) {
      qb.leftJoinAndSelect('wi.materialType', 'mt').andWhere({
        'mt.isBomable': true,
      });
    }

    if (!includeArchived) {
      qb.andWhere({ archived: false });
    }

    qb.andWhere({ [appType]: true });
  }

  private applyGrouping(qb: any, options: FetchItemsOptions) {
    const { groupHow = 0, workItemColumnIds = [] } = options;

    if (groupHow === 0) {
      qb.groupBy('wi.id');
      return;
    }

    if (groupHow === 1 || groupHow === 2) {
      if (!workItemColumnIds.length) {
        throw new BadRequestException('Column IDs are required for grouping');
      }

      const groupColumns = workItemColumnIds.map((col) => `wi.${col}`);
      if (groupHow === 2) {
        groupColumns.unshift('wi.spoolId');
      }

      qb.groupBy(groupColumns.join(', ')).addGroupBy('wi.rejected');
    } else if (groupHow === 3) {
      qb.groupBy([
        'wi.jobId',
        'wi.packageId',
        'wi.heatNumber',
        'wi.vendor',
        'wi.materialTypeId',
        'wi.size',
      ]);
    }

    // Add quantity calculation for grouped queries
    qb.select([
      '*',
      'CASE WHEN mt.isCut = 1 THEN SUM(wi.length) ELSE COUNT(DISTINCT wi.id) END as quantity',
    ]);
  }

  private applyJoins(qb: any, options: FetchItemsOptions) {
    const { isAssigned, currentUser } = options;

    if (isAssigned && currentUser) {
      qb.leftJoinAndSelect('spool.assignments', 'sa')
        .leftJoinAndSelect('pkg.assignments', 'pa')
        .leftJoinAndSelect('job.assignments', 'ja')
        .andWhere({
          $or: [
            { 'sa.userId': currentUser },
            { 'pa.userId': currentUser },
            { 'ja.userId': currentUser },
          ],
        });
    }
  }
}
