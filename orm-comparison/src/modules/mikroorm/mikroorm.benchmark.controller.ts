import { Controller, Get, Post, Put, Delete, Body, Param, Req } from '@nestjs/common';
import { MikroOrmService } from './mikroorm.service';
import { Request } from 'express';
import { Measure } from '../../common/decorators/performance.decorator';
import { Jobs } from './entities/jobs.entity';
import { Packages } from './entities/packages.entity';
import { Spools } from './entities/spools.entity';
import { WorkItems } from './entities/work_items.entity';

@Controller('mikroorm/benchmark')
export class MikroOrmBenchmarkController {
  constructor(private readonly mikroOrmService: MikroOrmService) {}

  @Post('all')
  @Measure()
  async benchmarkAll(@Req() req: Request) {
    const startTime = process.hrtime();

    // Jobs benchmark
    const jobsRepo = await this.mikroOrmService.getJobsRepository(req);
    const jobs = Array(10).fill(null).map((_, i) => jobsRepo.create({
      name: `Job ${i}`,
      status: 'pending'
    }));
    await jobsRepo.persistAndFlush(jobs);
    await jobsRepo.findAll();
    await jobsRepo.nativeDelete({});

    // Packages benchmark
    const packagesRepo = await this.mikroOrmService.getPackagesRepository(req);
    const packages = Array(20).fill(null).map((_, i) => packagesRepo.create({
      name: `Package ${i}`,
      jobId: i % 10 + 1,
      status: 'pending'
    }));
    await packagesRepo.persistAndFlush(packages);
    await packagesRepo.findAll();
    await packagesRepo.nativeDelete({});

    // Spools benchmark
    const spoolsRepo = await this.mikroOrmService.getSpoolsRepository(req);
    const spools = Array(30).fill(null).map((_, i) => spoolsRepo.create({
      name: `Spool ${i}`,
      packageId: i % 20 + 1,
      status: 'pending'
    }));
    await spoolsRepo.persistAndFlush(spools);
    await spoolsRepo.findAll();
    await spoolsRepo.nativeDelete({});

    // WorkItems benchmark
    const workItemsRepo = await this.mikroOrmService.getWorkItemsRepository(req);
    const workItems = Array(40).fill(null).map((_, i) => workItemsRepo.create({
      name: `WorkItem ${i}`,
      spoolId: i % 30 + 1,
      status: 'pending'
    }));
    await workItemsRepo.persistAndFlush(workItems);
    await workItemsRepo.findAll();
    await workItemsRepo.nativeDelete({});

    const endTime = process.hrtime(startTime);
    const duration = (endTime[0] * 1000000000 + endTime[1]) / 1000000;

    return {
      duration,
      operation: 'benchmark-all',
      orm: 'mikroorm'
    };
  }
}
