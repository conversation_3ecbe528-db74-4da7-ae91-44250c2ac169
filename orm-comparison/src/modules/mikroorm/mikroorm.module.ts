import { Module } from '@nestjs/common';
import { MikroOrmService } from './mikroorm.service';
import { MikroOrmUserController } from './mikroorm.controller';
import { MikroOrmBenchmarkController } from './mikroorm.benchmark.controller';

@Module({
  providers: [MikroOrmService],
  controllers: [MikroOrmUserController, MikroOrmBenchmarkController],
  exports: [MikroOrmService],
})
export class MikroOrmUserModule {}
