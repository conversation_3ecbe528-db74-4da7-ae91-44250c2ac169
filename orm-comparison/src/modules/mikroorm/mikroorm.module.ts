import { Module } from '@nestjs/common';
import { MikroOrmModule as NestMikroOrmModule } from '@mikro-orm/nestjs';
import { MikroOrmService } from './mikroorm.service';
import { MikroOrmUserController } from './mikroorm.controller';
import { MikroOrmBenchmarkController } from './mikroorm.benchmark.controller';
import { User } from './entities/user.entity';
import { Jobs } from './entities/jobs.entity';
import { Packages } from './entities/packages.entity';
import { Spools } from './entities/spools.entity';
import { WorkItems } from './entities/work_items.entity';

@Module({
  imports: [
    NestMikroOrmModule.forFeature([User, Jobs, Packages, Spools, WorkItems]),
  ],
  providers: [MikroOrmService],
  controllers: [MikroOrmUserController, MikroOrmBenchmarkController],
  exports: [MikroOrmService],
})
export class MikroOrmUserModule {}
