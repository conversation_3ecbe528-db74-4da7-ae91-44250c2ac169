import { Injectable, OnApplicationShutdown } from '@nestjs/common';
import { createConnection, Connection } from 'mysql2/promise';
import { getDatabaseConfig } from '../../config/database.config';

@Injectable()
export class DatabaseService implements OnApplicationShutdown {
  private connections: Map<string, Connection> = new Map();

  async getConnection(tenantId: string): Promise<Connection> {
    if (!this.connections.has(tenantId)) {
      const config = getDatabaseConfig(tenantId);
      const connection = await createConnection({
        host: config.host,
        port: config.port,
        user: config.user,
        password: config.password,
        database: config.database,
        multipleStatements: true,
      });
      this.connections.set(tenantId, connection);
    }
    return this.connections.get(tenantId)!;
  }

  async executeQuery(tenantId: string, query: string, params: any[] = []): Promise<any> {
    const connection = await this.getConnection(tenantId);
    const [results] = await connection.execute(query, params);
    return results;
  }

  async callStoredProcedure(tenantId: string, procedureName: string, params: any[] = []): Promise<any> {
    const connection = await this.getConnection(tenantId);
    const placeholders = params.map(() => '?').join(', ');
    const query = `CALL ${procedureName}(${placeholders})`;
    const [results] = await connection.execute(query, params);
    return results;
  }

  async onApplicationShutdown() {
    for (const connection of this.connections.values()) {
      await connection.end();
    }
    this.connections.clear();
  }
}
