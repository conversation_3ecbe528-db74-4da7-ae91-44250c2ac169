import { Injectable, BadRequestException } from '@nestjs/common';
import { Request } from 'express';
import { EnvironmentConfig } from '../../config/environment.config';
import * as mysql from 'mysql2/promise';

export interface StoredProcedureResult {
  duration: number;
  operation: string;
  method: string;
  rowsAffected?: number;
  data?: any[];
}

export interface FetchItemsParams {
  workItemColumnIds?: string;
  jobIds?: string;
  packageIds?: string;
  drawingIds?: string;
  stageIds?: string;
  bom?: boolean;
  isAssigned?: boolean;
  includeArchived?: boolean;
  currentUser?: number;
  defaultRound?: number;
  groupHow?: number;
  appType?: string;
}

@Injectable()
export class StoredProcedureService {
  private connections: Map<string, mysql.Connection> = new Map();

  async getConnection(req: Request): Promise<mysql.Connection> {
    const tenantId = (req as any).tenantId;
    if (!tenantId) {
      throw new BadRequestException('Tenant ID is required');
    }

    if (!this.connections.has(tenantId)) {
      const dbConfig = EnvironmentConfig.getDatabaseConfig(tenantId);
      const connection = await mysql.createConnection({
        host: dbConfig.host,
        port: dbConfig.port,
        user: dbConfig.user,
        password: dbConfig.password,
        database: dbConfig.database,
        multipleStatements: true,
      });
      this.connections.set(tenantId, connection);
    }

    return this.connections.get(tenantId)!;
  }

  // Basic CRUD stored procedures
  async createUser(req: Request, name: string, email: string, status: boolean = true): Promise<StoredProcedureResult> {
    const connection = await this.getConnection(req);
    const startTime = process.hrtime();

    try {
      const [results] = await connection.execute(
        'CALL create_user(?, ?, ?)',
        [name, email, status]
      );

      const endTime = process.hrtime(startTime);
      const duration = (endTime[0] * 1000000000 + endTime[1]) / 1000000;

      return {
        duration,
        operation: 'create_user',
        method: 'stored_procedure',
        data: results as any[],
      };
    } catch (error) {
      throw new BadRequestException(`Stored procedure error: ${error.message}`);
    }
  }

  async getUsers(req: Request): Promise<StoredProcedureResult> {
    const connection = await this.getConnection(req);
    const startTime = process.hrtime();

    try {
      const [results] = await connection.execute('CALL get_users()');

      const endTime = process.hrtime(startTime);
      const duration = (endTime[0] * 1000000000 + endTime[1]) / 1000000;

      return {
        duration,
        operation: 'get_users',
        method: 'stored_procedure',
        data: results as any[],
      };
    } catch (error) {
      throw new BadRequestException(`Stored procedure error: ${error.message}`);
    }
  }

  async getUserById(req: Request, id: number): Promise<StoredProcedureResult> {
    const connection = await this.getConnection(req);
    const startTime = process.hrtime();

    try {
      const [results] = await connection.execute(
        'CALL get_user_by_id(?)',
        [id]
      );

      const endTime = process.hrtime(startTime);
      const duration = (endTime[0] * 1000000000 + endTime[1]) / 1000000;

      return {
        duration,
        operation: 'get_user_by_id',
        method: 'stored_procedure',
        data: results as any[],
      };
    } catch (error) {
      throw new BadRequestException(`Stored procedure error: ${error.message}`);
    }
  }

  async updateUser(req: Request, id: number, name?: string, email?: string, status?: boolean): Promise<StoredProcedureResult> {
    const connection = await this.getConnection(req);
    const startTime = process.hrtime();

    try {
      const [results] = await connection.execute(
        'CALL update_user(?, ?, ?, ?)',
        [id, name, email, status]
      );

      const endTime = process.hrtime(startTime);
      const duration = (endTime[0] * 1000000000 + endTime[1]) / 1000000;

      return {
        duration,
        operation: 'update_user',
        method: 'stored_procedure',
        data: results as any[],
      };
    } catch (error) {
      throw new BadRequestException(`Stored procedure error: ${error.message}`);
    }
  }

  async deleteUser(req: Request, id: number): Promise<StoredProcedureResult> {
    const connection = await this.getConnection(req);
    const startTime = process.hrtime();

    try {
      const [results] = await connection.execute(
        'CALL delete_user(?)',
        [id]
      );

      const endTime = process.hrtime(startTime);
      const duration = (endTime[0] * 1000000000 + endTime[1]) / 1000000;

      return {
        duration,
        operation: 'delete_user',
        method: 'stored_procedure',
        rowsAffected: (results as any).affectedRows,
      };
    } catch (error) {
      throw new BadRequestException(`Stored procedure error: ${error.message}`);
    }
  }

  // Benchmark stored procedure
  async benchmarkOperations(req: Request): Promise<StoredProcedureResult> {
    const connection = await this.getConnection(req);
    const startTime = process.hrtime();

    try {
      const [results] = await connection.execute('CALL benchmark_operations()');

      const endTime = process.hrtime(startTime);
      const duration = (endTime[0] * 1000000000 + endTime[1]) / 1000000;

      return {
        duration,
        operation: 'benchmark_operations',
        method: 'stored_procedure',
        data: results as any[],
      };
    } catch (error) {
      throw new BadRequestException(`Stored procedure error: ${error.message}`);
    }
  }

  // Complex stored procedure - sp_fetch_items
  async fetchItems(req: Request, params: FetchItemsParams): Promise<StoredProcedureResult> {
    const connection = await this.getConnection(req);
    const startTime = process.hrtime();

    try {
      const [results] = await connection.execute(
        'CALL sp_fetch_items(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [
          params.workItemColumnIds || null,
          params.jobIds || null,
          params.packageIds || null,
          params.drawingIds || null,
          params.stageIds || null,
          params.bom ? 1 : 0,
          params.isAssigned ? 1 : 0,
          params.includeArchived ? 1 : 0,
          params.currentUser || null,
          params.defaultRound || 1,
          params.groupHow || 0,
          params.appType || 'fab',
        ]
      );

      const endTime = process.hrtime(startTime);
      const duration = (endTime[0] * 1000000000 + endTime[1]) / 1000000;

      return {
        duration,
        operation: 'sp_fetch_items',
        method: 'stored_procedure',
        data: results as any[],
      };
    } catch (error) {
      throw new BadRequestException(`Stored procedure error: ${error.message}`);
    }
  }

  async closeConnections(): Promise<void> {
    for (const connection of this.connections.values()) {
      await connection.end();
    }
    this.connections.clear();
  }
}
