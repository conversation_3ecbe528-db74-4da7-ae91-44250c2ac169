import { Controller, Get, Post, Put, Delete, Body, Param, Req, Query } from '@nestjs/common';
import { StoredProcedureService, FetchItemsParams } from './stored-procedure.service';
import { CreateUserDto, UpdateUserDto } from '../../shared/interfaces/user.dto';
import { Request } from 'express';
import { Measure } from '../../common/decorators/performance.decorator';

@Controller('stored-procedures')
export class StoredProcedureController {
  constructor(private readonly storedProcedureService: StoredProcedureService) {}

  @Post('users')
  @Measure()
  async createUser(@Body() createUserDto: CreateUserDto, @Req() req: Request) {
    return this.storedProcedureService.createUser(
      req,
      createUserDto.name,
      createUserDto.email,
      createUserDto.status ?? true
    );
  }

  @Get('users')
  @Measure()
  async getUsers(@Req() req: Request) {
    return this.storedProcedureService.getUsers(req);
  }

  @Get('users/:id')
  @Measure()
  async getUserById(@Param('id') id: string, @Req() req: Request) {
    return this.storedProcedureService.getUserById(req, parseInt(id));
  }

  @Put('users/:id')
  @Measure()
  async updateUser(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @Req() req: Request,
  ) {
    return this.storedProcedureService.updateUser(
      req,
      parseInt(id),
      updateUserDto.name,
      updateUserDto.email,
      updateUserDto.status
    );
  }

  @Delete('users/:id')
  @Measure()
  async deleteUser(@Param('id') id: string, @Req() req: Request) {
    return this.storedProcedureService.deleteUser(req, parseInt(id));
  }

  @Post('benchmark')
  @Measure()
  async benchmark(@Req() req: Request) {
    return this.storedProcedureService.benchmarkOperations(req);
  }

  @Post('fetch-items')
  @Measure()
  async fetchItems(@Body() params: FetchItemsParams, @Req() req: Request) {
    return this.storedProcedureService.fetchItems(req, params);
  }

  @Get('fetch-items')
  @Measure()
  async fetchItemsGet(@Query() query: any, @Req() req: Request) {
    const params: FetchItemsParams = {
      workItemColumnIds: query.workItemColumnIds,
      jobIds: query.jobIds,
      packageIds: query.packageIds,
      drawingIds: query.drawingIds,
      stageIds: query.stageIds,
      bom: query.bom === 'true',
      isAssigned: query.isAssigned === 'true',
      includeArchived: query.includeArchived === 'true',
      currentUser: query.currentUser ? parseInt(query.currentUser) : undefined,
      defaultRound: query.defaultRound ? parseInt(query.defaultRound) : undefined,
      groupHow: query.groupHow ? parseInt(query.groupHow) : undefined,
      appType: query.appType,
    };
    return this.storedProcedureService.fetchItems(req, params);
  }
}
