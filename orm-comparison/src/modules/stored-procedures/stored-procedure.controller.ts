import { Controller, Get, Post, Put, Delete, Body, Param, Req, Query } from '@nestjs/common';
import { StoredProcedureService, FetchItemsParams } from './stored-procedure.service';
import { CreateUserDto, UpdateUserDto } from '../../shared/interfaces/user.dto';
import { Request } from 'express';
import { Measure } from '../../common/decorators/performance.decorator';

@Controller('stored-procedures')
export class StoredProcedureController {
  constructor(private readonly storedProcedureService: StoredProcedureService) {}

  // Focus only on sp_fetch_items for now

  @Post('fetch-items')
  @Measure()
  async fetchItems(@Body() params: FetchItemsParams, @Req() req: Request) {
    return this.storedProcedureService.fetchItems(req, params);
  }

  @Get('fetch-items')
  @Measure()
  async fetchItemsGet(@Query() query: any, @Req() req: Request) {
    const params: FetchItemsParams = {
      workItemColumnIds: query.workItemColumnIds,
      jobIds: query.jobIds,
      packageIds: query.packageIds,
      drawingIds: query.drawingIds,
      stageIds: query.stageIds,
      bom: query.bom === 'true',
      isAssigned: query.isAssigned === 'true',
      includeArchived: query.includeArchived === 'true',
      currentUser: query.currentUser ? parseInt(query.currentUser) : undefined,
      defaultRound: query.defaultRound ? parseInt(query.defaultRound) : undefined,
      groupHow: query.groupHow ? parseInt(query.groupHow) : undefined,
      appType: query.appType,
    };
    return this.storedProcedureService.fetchItems(req, params);
  }
}
