import { Modu<PERSON> } from '@nestjs/common';
import { BenchmarkController } from './benchmark.controller';
import { TypeOrmUserModule } from '../typeorm/typeorm.module';
import { MikroOrmUserModule } from '../mikroorm/mikroorm.module';
import { PrismaModule } from '../prisma/prisma.module';
import { StoredProcedureModule } from '../stored-procedures/stored-procedure.module';

@Module({
  imports: [
    TypeOrmUserModule,
    MikroOrmUserModule,
    PrismaModule,
    StoredProcedureModule,
  ],
  controllers: [BenchmarkController],
})
export class BenchmarkModule {}
