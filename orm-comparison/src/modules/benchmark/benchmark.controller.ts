import { Controller, Post, Get, Req, Body, Query } from '@nestjs/common';
import { Request } from 'express';
import { Measure } from '../../common/decorators/performance.decorator';
import { TypeOrmService } from '../typeorm/services/typeorm.service';
import { MikroOrmService } from '../mikroorm/mikroorm.service';
import { PrismaService } from '../prisma/prisma.service';
import { StoredProcedureService, FetchItemsParams } from '../stored-procedures/stored-procedure.service';

export interface BenchmarkResult {
  operation: string;
  results: {
    typeorm_orm: any;
    typeorm_sp: any;
    mikroorm_orm: any;
    mikroorm_sp: any;
    prisma_orm: any;
    prisma_sp: any;
    direct_sp: any;
  };
  summary: {
    fastest: string;
    slowest: string;
    performance_comparison: { [key: string]: number };
  };
}

@Controller('benchmark')
export class BenchmarkController {
  constructor(
    private readonly typeormService: TypeOrmService,
    private readonly mikroormService: MikroOrmService,
    private readonly prismaService: PrismaService,
    private readonly storedProcedureService: StoredProcedureService,
  ) {}

  @Post('users/create')
  @Measure()
  async benchmarkCreateUser(@Body() userData: { name: string; email: string; status?: boolean }, @Req() req: Request): Promise<BenchmarkResult> {
    const { name, email, status = true } = userData;

    // TypeORM ORM
    const typeormRepo = await this.typeormService.setSchema(req);
    const typeormStart = process.hrtime();
    const typeormUser = typeormRepo.create({ name, email, status });
    await typeormRepo.save(typeormUser);
    const typeormEnd = process.hrtime(typeormStart);
    const typeormDuration = (typeormEnd[0] * 1000000000 + typeormEnd[1]) / 1000000;

    // TypeORM Stored Procedure
    const typeormSP = await this.typeormService.createUserSP(req, name, email, status);

    // MikroORM ORM
    const mikroormRepo = await this.mikroormService.getUserRepository(req);
    const mikroormOrm = await this.mikroormService.getClientOrm((req as any).tenantId);
    const mikroormStart = process.hrtime();
    const mikroormUser = mikroormRepo.create({ name, email, status, createdAt: new Date(), updatedAt: new Date() });
    await mikroormOrm.em.persistAndFlush(mikroormUser);
    const mikroormEnd = process.hrtime(mikroormStart);
    const mikroormDuration = (mikroormEnd[0] * 1000000000 + mikroormEnd[1]) / 1000000;

    // MikroORM Stored Procedure
    const mikroormSP = await this.mikroormService.createUserSP(req, name, email, status);

    // Prisma ORM
    const prisma = this.prismaService.setClientDatabase(req);
    const prismaStart = process.hrtime();
    await prisma.user.create({ data: { name, email, status } });
    const prismaEnd = process.hrtime(prismaStart);
    const prismaDuration = (prismaEnd[0] * 1000000000 + prismaEnd[1]) / 1000000;

    // Prisma Stored Procedure
    const prismaSP = await this.prismaService.createUserSP(req, name, email, status);

    // Direct Stored Procedure
    const directSP = await this.storedProcedureService.createUser(req, name, email, status);

    const results = {
      typeorm_orm: { duration: typeormDuration, method: 'typeorm_orm' },
      typeorm_sp: typeormSP,
      mikroorm_orm: { duration: mikroormDuration, method: 'mikroorm_orm' },
      mikroorm_sp: mikroormSP,
      prisma_orm: { duration: prismaDuration, method: 'prisma_orm' },
      prisma_sp: prismaSP,
      direct_sp: directSP,
    };

    return this.generateSummary('create_user', results);
  }

  @Get('users')
  @Measure()
  async benchmarkGetUsers(@Req() req: Request): Promise<BenchmarkResult> {
    // TypeORM ORM
    const typeormRepo = await this.typeormService.setSchema(req);
    const typeormStart = process.hrtime();
    await typeormRepo.find();
    const typeormEnd = process.hrtime(typeormStart);
    const typeormDuration = (typeormEnd[0] * 1000000000 + typeormEnd[1]) / 1000000;

    // TypeORM Stored Procedure
    const typeormSP = await this.typeormService.getUsersSP(req);

    // MikroORM ORM
    const mikroormRepo = await this.mikroormService.getUserRepository(req);
    const mikroormStart = process.hrtime();
    await mikroormRepo.findAll();
    const mikroormEnd = process.hrtime(mikroormStart);
    const mikroormDuration = (mikroormEnd[0] * 1000000000 + mikroormEnd[1]) / 1000000;

    // MikroORM Stored Procedure
    const mikroormSP = await this.mikroormService.getUsersSP(req);

    // Prisma ORM
    const prisma = this.prismaService.setClientDatabase(req);
    const prismaStart = process.hrtime();
    await prisma.user.findMany();
    const prismaEnd = process.hrtime(prismaStart);
    const prismaDuration = (prismaEnd[0] * 1000000000 + prismaEnd[1]) / 1000000;

    // Prisma Stored Procedure
    const prismaSP = await this.prismaService.getUsersSP(req);

    // Direct Stored Procedure
    const directSP = await this.storedProcedureService.getUsers(req);

    const results = {
      typeorm_orm: { duration: typeormDuration, method: 'typeorm_orm' },
      typeorm_sp: typeormSP,
      mikroorm_orm: { duration: mikroormDuration, method: 'mikroorm_orm' },
      mikroorm_sp: mikroormSP,
      prisma_orm: { duration: prismaDuration, method: 'prisma_orm' },
      prisma_sp: prismaSP,
      direct_sp: directSP,
    };

    return this.generateSummary('get_users', results);
  }

  @Post('benchmark-operations')
  @Measure()
  async benchmarkOperations(@Req() req: Request): Promise<BenchmarkResult> {
    // TypeORM Stored Procedure
    const typeormSP = await this.typeormService.benchmarkOperationsSP(req);

    // MikroORM Stored Procedure
    const mikroormSP = await this.mikroormService.benchmarkOperationsSP(req);

    // Prisma Stored Procedure
    const prismaSP = await this.prismaService.benchmarkOperationsSP(req);

    // Direct Stored Procedure
    const directSP = await this.storedProcedureService.benchmarkOperations(req);

    const results = {
      typeorm_orm: { duration: 0, method: 'not_applicable' },
      typeorm_sp: typeormSP,
      mikroorm_orm: { duration: 0, method: 'not_applicable' },
      mikroorm_sp: mikroormSP,
      prisma_orm: { duration: 0, method: 'not_applicable' },
      prisma_sp: prismaSP,
      direct_sp: directSP,
    };

    return this.generateSummary('benchmark_operations', results);
  }

  @Post('fetch-items')
  @Measure()
  async benchmarkFetchItems(@Body() params: FetchItemsParams, @Req() req: Request): Promise<BenchmarkResult> {
    // Direct Stored Procedure (sp_fetch_items)
    const directSP = await this.storedProcedureService.fetchItems(req, params);

    // TypeORM Stored Procedure
    const typeormSP = await this.typeormService.callStoredProcedure(req, 'sp_fetch_items', [
      params.workItemColumnIds || null,
      params.jobIds || null,
      params.packageIds || null,
      params.drawingIds || null,
      params.stageIds || null,
      params.bom ? 1 : 0,
      params.isAssigned ? 1 : 0,
      params.includeArchived ? 1 : 0,
      params.currentUser || null,
      params.defaultRound || 1,
      params.groupHow || 0,
      params.appType || 'fab',
    ]);

    // MikroORM Stored Procedure
    const mikroormSP = await this.mikroormService.callStoredProcedure(req, 'sp_fetch_items', [
      params.workItemColumnIds || null,
      params.jobIds || null,
      params.packageIds || null,
      params.drawingIds || null,
      params.stageIds || null,
      params.bom ? 1 : 0,
      params.isAssigned ? 1 : 0,
      params.includeArchived ? 1 : 0,
      params.currentUser || null,
      params.defaultRound || 1,
      params.groupHow || 0,
      params.appType || 'fab',
    ]);

    // Prisma Stored Procedure
    const prismaSP = await this.prismaService.callStoredProcedure(req, 'sp_fetch_items', [
      params.workItemColumnIds || null,
      params.jobIds || null,
      params.packageIds || null,
      params.drawingIds || null,
      params.stageIds || null,
      params.bom ? 1 : 0,
      params.isAssigned ? 1 : 0,
      params.includeArchived ? 1 : 0,
      params.currentUser || null,
      params.defaultRound || 1,
      params.groupHow || 0,
      params.appType || 'fab',
    ]);

    const results = {
      typeorm_orm: { duration: 0, method: 'not_applicable' },
      typeorm_sp: typeormSP,
      mikroorm_orm: { duration: 0, method: 'not_applicable' },
      mikroorm_sp: mikroormSP,
      prisma_orm: { duration: 0, method: 'not_applicable' },
      prisma_sp: prismaSP,
      direct_sp: directSP,
    };

    return this.generateSummary('sp_fetch_items', results);
  }

  private generateSummary(operation: string, results: any): BenchmarkResult {
    const durations = Object.entries(results)
      .filter(([key, value]: [string, any]) => value.duration > 0)
      .map(([key, value]: [string, any]) => ({ method: key, duration: value.duration }))
      .sort((a, b) => a.duration - b.duration);

    const fastest = durations[0]?.method || 'unknown';
    const slowest = durations[durations.length - 1]?.method || 'unknown';

    const performance_comparison: { [key: string]: number } = {};
    const fastestDuration = durations[0]?.duration || 1;

    durations.forEach(({ method, duration }) => {
      performance_comparison[method] = duration / fastestDuration;
    });

    return {
      operation,
      results,
      summary: {
        fastest,
        slowest,
        performance_comparison,
      },
    };
  }
}
