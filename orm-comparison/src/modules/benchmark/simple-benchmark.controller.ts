import { Controller, Post, Req, Get } from '@nestjs/common';
import { Request } from 'express';
import { PrismaService } from '../prisma/prisma.service';
import { StoredProcedureService } from '../stored-procedures/stored-procedure.service';
import { Measure } from '../../common/decorators/performance.decorator';

interface BenchmarkResult {
  operation: string;
  prisma_duration?: number;
  stored_procedure_duration?: number;
  prisma_error?: string;
  stored_procedure_error?: string;
  winner?: string;
  performance_gain?: string;
}

@Controller('benchmark')
export class SimpleBenchmarkController {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly storedProcedureService: StoredProcedureService,
  ) {}

  @Get('health')
  async healthCheck(@Req() req: Request) {
    return {
      message: 'Benchmark service is ready',
      tenant: (req as any).tenantId,
      timestamp: new Date().toISOString(),
    };
  }

  @Post('simple-query')
  @Measure()
  async benchmarkSimpleQuery(@Req() req: Request): Promise<BenchmarkResult> {
    const result: BenchmarkResult = {
      operation: 'simple_database_query',
    };

    // Test Prisma raw query
    try {
      const prisma = this.prismaService.setClientDatabase(req);
      const startTime = process.hrtime.bigint();
      
      await prisma.$queryRaw`SELECT 1 as test, NOW() as current_time`;
      
      const endTime = process.hrtime.bigint();
      result.prisma_duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
    } catch (error) {
      result.prisma_error = error.message;
    }

    // Test Stored Procedure approach (raw MySQL)
    try {
      const connection = await this.storedProcedureService.getConnection(req);
      const startTime = process.hrtime.bigint();
      
      await connection.execute('SELECT 1 as test, NOW() as current_time');
      
      const endTime = process.hrtime.bigint();
      result.stored_procedure_duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
    } catch (error) {
      result.stored_procedure_error = error.message;
    }

    // Determine winner
    if (result.prisma_duration && result.stored_procedure_duration) {
      if (result.prisma_duration < result.stored_procedure_duration) {
        result.winner = 'Prisma';
        const gain = ((result.stored_procedure_duration - result.prisma_duration) / result.stored_procedure_duration * 100).toFixed(2);
        result.performance_gain = `${gain}% faster`;
      } else {
        result.winner = 'Stored Procedure (Raw MySQL)';
        const gain = ((result.prisma_duration - result.stored_procedure_duration) / result.prisma_duration * 100).toFixed(2);
        result.performance_gain = `${gain}% faster`;
      }
    }

    return result;
  }

  @Post('table-count')
  @Measure()
  async benchmarkTableCount(@Req() req: Request): Promise<BenchmarkResult> {
    const result: BenchmarkResult = {
      operation: 'table_row_count',
    };

    // Test Prisma
    try {
      const prisma = this.prismaService.setClientDatabase(req);
      const startTime = process.hrtime.bigint();
      
      // Try to count rows from a common table (adjust table name as needed)
      const count = await prisma.$queryRaw`SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE()`;
      
      const endTime = process.hrtime.bigint();
      result.prisma_duration = Number(endTime - startTime) / 1000000;
    } catch (error) {
      result.prisma_error = error.message;
    }

    // Test Raw MySQL
    try {
      const connection = await this.storedProcedureService.getConnection(req);
      const startTime = process.hrtime.bigint();
      
      await connection.execute('SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE()');
      
      const endTime = process.hrtime.bigint();
      result.stored_procedure_duration = Number(endTime - startTime) / 1000000;
    } catch (error) {
      result.stored_procedure_error = error.message;
    }

    // Determine winner
    if (result.prisma_duration && result.stored_procedure_duration) {
      if (result.prisma_duration < result.stored_procedure_duration) {
        result.winner = 'Prisma';
        const gain = ((result.stored_procedure_duration - result.prisma_duration) / result.stored_procedure_duration * 100).toFixed(2);
        result.performance_gain = `${gain}% faster`;
      } else {
        result.winner = 'Raw MySQL';
        const gain = ((result.prisma_duration - result.stored_procedure_duration) / result.prisma_duration * 100).toFixed(2);
        result.performance_gain = `${gain}% faster`;
      }
    }

    return result;
  }

  @Post('multiple-queries')
  @Measure()
  async benchmarkMultipleQueries(@Req() req: Request): Promise<BenchmarkResult> {
    const result: BenchmarkResult = {
      operation: 'multiple_queries_10x',
    };

    const queryCount = 10;

    // Test Prisma
    try {
      const prisma = this.prismaService.setClientDatabase(req);
      const startTime = process.hrtime.bigint();
      
      for (let i = 0; i < queryCount; i++) {
        await prisma.$queryRaw`SELECT ${i} as iteration, NOW() as current_time`;
      }
      
      const endTime = process.hrtime.bigint();
      result.prisma_duration = Number(endTime - startTime) / 1000000;
    } catch (error) {
      result.prisma_error = error.message;
    }

    // Test Raw MySQL
    try {
      const connection = await this.storedProcedureService.getConnection(req);
      const startTime = process.hrtime.bigint();
      
      for (let i = 0; i < queryCount; i++) {
        await connection.execute('SELECT ? as iteration, NOW() as current_time', [i]);
      }
      
      const endTime = process.hrtime.bigint();
      result.stored_procedure_duration = Number(endTime - startTime) / 1000000;
    } catch (error) {
      result.stored_procedure_error = error.message;
    }

    // Determine winner
    if (result.prisma_duration && result.stored_procedure_duration) {
      if (result.prisma_duration < result.stored_procedure_duration) {
        result.winner = 'Prisma';
        const gain = ((result.stored_procedure_duration - result.prisma_duration) / result.stored_procedure_duration * 100).toFixed(2);
        result.performance_gain = `${gain}% faster`;
      } else {
        result.winner = 'Raw MySQL';
        const gain = ((result.prisma_duration - result.stored_procedure_duration) / result.prisma_duration * 100).toFixed(2);
        result.performance_gain = `${gain}% faster`;
      }
    }

    return result;
  }

  @Get('summary')
  async getBenchmarkSummary(@Req() req: Request) {
    return {
      message: 'ORM Performance Benchmark Summary',
      tenant: (req as any).tenantId,
      available_tests: [
        'POST /benchmark/simple-query - Basic SELECT query comparison',
        'POST /benchmark/table-count - Table counting comparison', 
        'POST /benchmark/multiple-queries - Multiple query execution comparison',
      ],
      note: 'Each test compares Prisma ORM vs Raw MySQL performance',
    };
  }
}
