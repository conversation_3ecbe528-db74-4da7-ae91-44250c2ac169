import { Modu<PERSON> } from '@nestjs/common';
import { SimpleBenchmarkController } from './simple-benchmark.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { StoredProcedureModule } from '../stored-procedures/stored-procedure.module';

@Module({
  imports: [PrismaModule, StoredProcedureModule],
  controllers: [SimpleBenchmarkController],
})
export class SimpleBenchmarkModule {}
