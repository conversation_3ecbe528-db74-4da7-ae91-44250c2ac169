import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { TypeOrmService } from './services/typeorm.service';
import { TypeOrmUserController } from './typeorm.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Jobs, Packages, Spools, WorkItems])
  ],
  providers: [TypeOrmService],
  controllers: [TypeOrmUserController],
  exports: [TypeOrmService],
})
export class TypeOrmUserModule {}
