import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { Jobs } from './entities/jobs.entity';
import { Packages } from './entities/packages.entity';
import { Spools } from './entities/spools.entity';
import { WorkItems } from './entities/work_items.entity';
import { TypeOrmService } from './services/typeorm.service';
import { TypeOrmUserController } from './typeorm.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Jobs, Packages, Spools, WorkItems]),
  ],
  providers: [TypeOrmService],
  controllers: [TypeOrmUserController],
  exports: [TypeOrmService],
})
export class TypeOrmUserModule {}
