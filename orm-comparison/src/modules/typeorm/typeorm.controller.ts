import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Req,
} from '@nestjs/common';
import { TypeOrmService } from './services/typeorm.service';
import { CreateUserDto, UpdateUserDto } from '../../shared/interfaces/user.dto';
import { Request } from 'express';
import { Measure } from '../../common/decorators/performance.decorator';
import { User } from './entities/user.entity';

@Controller('typeorm/users')
export class TypeOrmUserController {
  constructor(private readonly typeormService: TypeOrmService) {}

  @Post()
  @Measure()
  async create(@Body() createUserDto: CreateUserDto, @Req() req: Request) {
    return this.typeormService.createUserSP(req, createUserDto.name, createUserDto.email, createUserDto.status);
  }

  @Get()
  @Measure()
  async findAll(@Req() req: Request) {
    return this.typeormService.getUsersSP(req);
  }

  @Get(':id')
  @Measure()
  async findOne(@Param('id') id: string, @Req() req: Request) {
    return this.typeormService.getUserByIdSP(req, parseInt(id));
  }

  @Put(':id')
  @Measure()
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @Req() req: Request,
  ) {
    return this.typeormService.updateUserSP(req, parseInt(id), updateUserDto.name, updateUserDto.email, updateUserDto.status);
  }

  @Delete(':id')
  @Measure()
  async remove(@Param('id') id: string, @Req() req: Request) {
    return this.typeormService.deleteUserSP(req, parseInt(id));
  }

  @Post('benchmark')
  @Measure()
  async benchmark(@Req() req: Request) {
    const repository = await this.typeormService.setSchema(req);
    const startTime = process.hrtime();

    // Create 100 users
    const users: User[] = [];
    for (let i = 0; i < 100; i++) {
      const user = repository.create({
        name: `User ${i}`,
        email: `user${i}@example.com`,
        status: true,
      });
      users.push(await repository.save(user));
    }

    // Read all users
    await repository.find();

    // Update all users
    await Promise.all(
      users.map((user) => repository.update(user.id, { status: false })),
    );

    // Delete all users
    await repository.delete(users.map((user) => user.id));

    const endTime = process.hrtime(startTime);
    const duration = (endTime[0] * 1000000000 + endTime[1]) / 1000000; // Convert to milliseconds

    return { duration, operation: 'benchmark', orm: 'typeorm' };
  }

  // Stored procedure endpoints
  @Post('sp/users')
  @Measure()
  async createUserSP(@Body() createUserDto: CreateUserDto, @Req() req: Request) {
    return this.typeormService.createUserSP(req, createUserDto.name, createUserDto.email, createUserDto.status ?? true);
  }

  @Get('sp/users')
  @Measure()
  async getUsersSP(@Req() req: Request) {
    return this.typeormService.getUsersSP(req);
  }

  @Get('sp/users/:id')
  @Measure()
  async getUserByIdSP(@Param('id') id: string, @Req() req: Request) {
    return this.typeormService.getUserByIdSP(req, parseInt(id));
  }

  @Put('sp/users/:id')
  @Measure()
  async updateUserSP(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @Req() req: Request,
  ) {
    return this.typeormService.updateUserSP(req, parseInt(id), updateUserDto.name, updateUserDto.email, updateUserDto.status);
  }

  @Delete('sp/users/:id')
  @Measure()
  async deleteUserSP(@Param('id') id: string, @Req() req: Request) {
    return this.typeormService.deleteUserSP(req, parseInt(id));
  }

  @Post('sp/benchmark')
  @Measure()
  async benchmarkSP(@Req() req: Request) {
    return this.typeormService.benchmarkOperationsSP(req);
  }
}
