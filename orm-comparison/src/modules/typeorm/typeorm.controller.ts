import { Controller, Get, Post, Put, Delete, Body, Param, Req } from '@nestjs/common';
import { TypeOrmService } from './services/typeorm.service';
import { CreateUserDto, UpdateUserDto } from '../../shared/interfaces/user.dto';
import { Request } from 'express';
import { Measure } from '../../common/decorators/performance.decorator';
import { User } from './entities/user.entity';

@Controller('typeorm/users')
export class TypeOrmUserController {
  constructor(private readonly typeormService: TypeOrmService) {}

  @Post()
  @Measure()
  async create(@Body() createUserDto: CreateUserDto, @Req() req: Request) {
    const repository = await this.typeormService.setSchema(req);
    const user = repository.create(createUserDto);
    return repository.save(user);
  }

  @Get()
  @Measure()
  async findAll(@Req() req: Request) {
    const repository = await this.typeormService.setSchema(req);
    return repository.find();
  }

  @Get(':id')
  @Measure()
  async findOne(@Param('id') id: string, @Req() req: Request) {
    const repository = await this.typeormService.setSchema(req);
    return repository.findOne({ where: { id: parseInt(id) } });
  }

  @Put(':id')
  @Measure()
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @Req() req: Request,
  ) {
    const repository = await this.typeormService.setSchema(req);
    await repository.update(parseInt(id), updateUserDto);
    return repository.findOne({ where: { id: parseInt(id) } });
  }

  @Delete(':id')
  @Measure()
  async remove(@Param('id') id: string, @Req() req: Request) {
    const repository = await this.typeormService.setSchema(req);
    const user = await repository.findOne({ where: { id: parseInt(id) } });
    return repository.remove(user);
  }

  @Post('benchmark')
  @Measure()
  async benchmark(@Req() req: Request) {
    const repository = await this.typeormService.setSchema(req);
    const startTime = process.hrtime();

    // Create 100 users
    const users: User[] = [];
    for (let i = 0; i < 100; i++) {
      const user = repository.create({
        name: `User ${i}`,
        email: `user${i}@example.com`,
        status: true,
      });
      users.push(await repository.save(user));
    }

    // Read all users
    await repository.find();

    // Update all users
    await Promise.all(
      users.map((user) =>
        repository.update(user.id, { status: false }),
      ),
    );

    // Delete all users
    await repository.delete(users.map((user) => user.id));

    const endTime = process.hrtime(startTime);
    const duration = (endTime[0] * 1000000000 + endTime[1]) / 1000000; // Convert to milliseconds

    return { duration, operation: 'benchmark', orm: 'typeorm' };
  }
}
