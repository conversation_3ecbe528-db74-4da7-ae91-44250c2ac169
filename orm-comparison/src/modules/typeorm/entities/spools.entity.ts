import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { ISpools } from '../../../shared/interfaces/spools.interface';
import { WorkItems } from './work_items.entity';

@Entity('spools')
export class Spools implements ISpools {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  packageId: number;

  @Column()
  status: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => WorkItems, (workItem) => workItem.spool)
  workItems: WorkItems[];
}
