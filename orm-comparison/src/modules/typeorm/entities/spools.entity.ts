import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ISpools } from '../../../shared/interfaces/spools.interface';

@Entity('spools')
export class Spools implements ISpools {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  packageId: number;

  @Column()
  status: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
