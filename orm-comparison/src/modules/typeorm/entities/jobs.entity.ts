import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { IJobs } from '../../../shared/interfaces/jobs.interface';
import { WorkItems } from './work_items.entity';

@Entity('jobs')
export class Jobs implements IJobs {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  status: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => WorkItems, (workItem) => workItem.job)
  workItems: WorkItems[];
}
