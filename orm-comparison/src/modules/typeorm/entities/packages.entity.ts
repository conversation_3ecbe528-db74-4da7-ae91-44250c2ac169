import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { IPackages } from '../../../shared/interfaces/packages.interface';
import { WorkItems } from './work_items.entity';

@Entity('packages')
export class Packages implements IPackages {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  jobId: number;

  @Column()
  status: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => WorkItems, workItem => workItem.package)
  workItems: WorkItems[];
}
