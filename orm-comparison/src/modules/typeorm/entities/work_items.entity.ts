import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { IWorkItems } from '../../../shared/interfaces/work_items.interface';
import { Jobs } from './jobs.entity';
import { Packages } from './packages.entity';
import { Spools } from './spools.entity';
import { WorkItemStage } from './work-item-stage.entity';

@Entity('work_items')
export class WorkItems implements IWorkItems {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ nullable: true })
  revitItemId: string;

  @Column({ nullable: true })
  tagNumber: string;

  @Column({ nullable: true })
  endPrep1: string;

  @Column({ nullable: true })
  endPrep2: string;

  @Column({ nullable: true })
  endPrep3: string;

  @Column({ nullable: true })
  endPrep4: string;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  length: number;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  roundedCutLength: number;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  stockLength: number;

  @Column({ nullable: true })
  identifier: string;

  @Column({ nullable: true })
  measurementArea: string;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  height: number;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  width: number;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  thickness: number;

  @Column({ nullable: true })
  paintSpec: string;

  @Column({ nullable: true })
  texture: string;

  @Column({ nullable: true })
  fixtureType: string;

  @Column({ nullable: true })
  gauge: string;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  weight: number;

  @Column({ nullable: true })
  hangerSize: string;

  @Column({ nullable: true })
  productCode: string;

  @Column({ nullable: true })
  insulation: string;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  insulationArea: number;

  @Column({ nullable: true })
  insulationGauge: string;

  @Column({ nullable: true })
  insulationSpecification: string;

  @Column()
  joiningProcedureId: number;

  @Column()
  materialTypeId: number;

  @Column({ nullable: true })
  serviceName: string;

  @Column({ nullable: true })
  serviceColorName: string;

  @Column()
  status: string;

  @Column({ nullable: true })
  size: string;

  @Column({ nullable: true })
  area: string;

  @Column({ type: 'boolean', default: false })
  randomLength: boolean;

  @Column({ nullable: true })
  rodSize: string;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  supportRodLength: number;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  supportRodLength2: number;

  @Column({ nullable: true })
  laydownLocationId: number;

  @Column({ nullable: true })
  linerSpec: string;

  @Column({ nullable: true })
  heatNumber: string;

  @Column({ nullable: true })
  containerName: string;

  @Column({ type: 'boolean', default: false })
  archived: boolean;

  @Column({ nullable: true })
  fillerMetal: string;

  @Column({ nullable: true })
  vendor: string;

  @Column()
  jobId: number;

  @Column()
  packageId: number;

  @Column()
  spoolId: number;

  @Column({ type: 'boolean', default: false })
  deleted: boolean;

  @Column({ type: 'boolean', default: true })
  fab: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => Jobs, (job) => job.workItems)
  @JoinColumn({ name: 'job_id' })
  job: Jobs;

  @ManyToOne(() => Packages, (pkg) => pkg.workItems)
  @JoinColumn({ name: 'package_id' })
  package: Packages;

  @ManyToOne(() => Spools, (spool) => spool.workItems)
  @JoinColumn({ name: 'drawing_id' })
  spool: Spools;

  @OneToMany(() => WorkItemStage, (stage) => stage.workItem)
  stages: WorkItemStage[];
}
