import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { WorkItems } from './work_items.entity';

@Entity('work_item_stages')
export class WorkItemStage {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  workItemId: number;

  @Column()
  stageId: number;

  @Column({ type: 'boolean', default: false })
  available: boolean;

  @Column({ type: 'boolean', default: false })
  completed: boolean;

  @Column({ type: 'boolean', default: false })
  deleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => WorkItems, workItem => workItem.stages)
  @JoinColumn({ name: 'work_item_id' })
  workItem: WorkItems;
}
