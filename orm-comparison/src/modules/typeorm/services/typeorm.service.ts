import {
  Injectable,
  BadRequestException,
  OnApplicationShutdown,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { User } from '../entities/user.entity';
import { Jobs } from '../entities/jobs.entity';
import { Packages } from '../entities/packages.entity';
import { Spools } from '../entities/spools.entity';
import { WorkItems } from '../entities/work_items.entity';
import { Request } from 'express';
import { EnvironmentConfig } from '../../../config/environment.config';

@Injectable()
export class TypeOrmService implements OnApplicationShutdown {
  private clientConnections: Map<string, DataSource> = new Map();

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Jobs)
    private readonly jobsRepository: Repository<Jobs>,
    @InjectRepository(Packages)
    private readonly packagesRepository: Repository<Packages>,
    @InjectRepository(Spools)
    private readonly spoolsRepository: Repository<Spools>,
    @InjectRepository(WorkItems)
    private readonly workItemsRepository: Repository<WorkItems>,
  ) {}

  private async getClientConnection(clientId: string): Promise<DataSource> {
    if (!this.clientConnections.has(clientId)) {
      const dbConfig = EnvironmentConfig.getDatabaseConfig(clientId);
      const connectionOptions = {
        type: 'mysql' as const,
        host: dbConfig.host,
        port: dbConfig.port,
        username: dbConfig.user,
        password: dbConfig.password,
        database: dbConfig.database,
        entities: [User, Jobs, Packages, Spools, WorkItems],
        synchronize: process.env.NODE_ENV !== 'production',
        autoLoadEntities: true,
      };
      const dataSource = new DataSource(connectionOptions);
      await dataSource.initialize();
      this.clientConnections.set(clientId, dataSource);
    }
    return this.clientConnections.get(clientId)!;
  }

  // Method to switch database based on tenant
  async setSchema(req: Request) {
    const clientId = (req as any).tenantId;
    if (!clientId) {
      throw new BadRequestException('Client ID is required');
    }

    try {
      const clientConnection = await this.getClientConnection(clientId);
      return clientConnection.getRepository(User);
    } catch (error) {
      throw new BadRequestException(
        `Failed to connect to client database: ${error.message}`,
      );
    }
  }

  async getJobsRepository(req: Request) {
    const clientId = (req as any).tenantId;
    if (!clientId) {
      throw new BadRequestException('Client ID is required');
    }
    const clientConnection = await this.getClientConnection(clientId);
    return clientConnection.getRepository(Jobs);
  }

  async getPackagesRepository(req: Request) {
    const clientId = (req as any).tenantId;
    if (!clientId) {
      throw new BadRequestException('Client ID is required');
    }
    const clientConnection = await this.getClientConnection(clientId);
    return clientConnection.getRepository(Packages);
  }

  async getSpoolsRepository(req: Request) {
    const clientId = (req as any).tenantId;
    if (!clientId) {
      throw new BadRequestException('Client ID is required');
    }
    const clientConnection = await this.getClientConnection(clientId);
    return clientConnection.getRepository(Spools);
  }

  async getWorkItemsRepository(req: Request) {
    const clientId = (req as any).tenantId;
    if (!clientId) {
      throw new BadRequestException('Client ID is required');
    }
    const clientConnection = await this.getClientConnection(clientId);
    return clientConnection.getRepository(WorkItems);
  }

  // Method to get performance metrics
  getQueryDuration(): number {
    return Date.now();
  }

  async onApplicationShutdown() {
    await Promise.all(
      Array.from(this.clientConnections.values()).map((conn) => conn.destroy()),
    );
    this.clientConnections.clear();
  }
}
