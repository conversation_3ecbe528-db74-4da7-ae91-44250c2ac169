import {
  Injectable,
  BadRequestException,
  OnApplicationShutdown,
} from '@nestjs/common';
import { DataSource } from 'typeorm';
import { Request } from 'express';
import { getDatabaseConfig } from '../../../config/database.config';

@Injectable()
export class TypeOrmService implements OnApplicationShutdown {
  private clientConnections: Map<string, DataSource> = new Map();

  constructor() {}

  private async getClientConnection(clientId: string): Promise<DataSource> {
    if (!this.clientConnections.has(clientId)) {
      const dbConfig = getDatabaseConfig(clientId);
      const connectionOptions = {
        type: 'mariadb' as const, // Use MariaDB as specified
        host: dbConfig.host,
        port: dbConfig.port,
        username: dbConfig.user,
        password: dbConfig.password,
        database: dbConfig.database,
        entities: [], // No entities to prevent schema operations
        synchronize: false, // NEVER synchronize production database
        autoLoadEntities: false, // Disable auto-loading
        logging: false, // Disable logging
      };
      const dataSource = new DataSource(connectionOptions);
      await dataSource.initialize();
      this.clientConnections.set(clientId, dataSource);
    }
    return this.clientConnections.get(clientId)!;
  }

  // Raw query methods for TypeORM without entities
  async executeRawQuery(req: Request, query: string, params: any[] = []): Promise<any> {
    const clientId = (req as any).tenantId;
    if (!clientId) {
      throw new BadRequestException('Client ID is required');
    }

    try {
      const clientConnection = await this.getClientConnection(clientId);
      const startTime = process.hrtime();

      const result = await clientConnection.query(query, params);

      const endTime = process.hrtime(startTime);
      const duration = (endTime[0] * 1000000000 + endTime[1]) / 1000000;

      return {
        duration,
        method: 'typeorm_raw_query',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException(
        `TypeORM raw query error: ${error.message}`,
      );
    }
  }

  // Method to get performance metrics
  getQueryDuration(): number {
    return Date.now();
  }

  // Stored procedure methods
  async callStoredProcedure(req: Request, procedureName: string, params: any[] = []): Promise<any> {
    const clientId = (req as any).tenantId;
    if (!clientId) {
      throw new BadRequestException('Client ID is required');
    }

    const clientConnection = await this.getClientConnection(clientId);
    const startTime = process.hrtime();

    try {
      const placeholders = params.map(() => '?').join(', ');
      const query = `CALL ${procedureName}(${placeholders})`;
      const result = await clientConnection.query(query, params);

      const endTime = process.hrtime(startTime);
      const duration = (endTime[0] * 1000000000 + endTime[1]) / 1000000;

      return {
        duration,
        operation: procedureName,
        method: 'typeorm_stored_procedure',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException(`TypeORM stored procedure error: ${error.message}`);
    }
  }

  async createUserSP(req: Request, name: string, email: string, status: boolean = true): Promise<any> {
    return this.callStoredProcedure(req, 'create_user', [name, email, status]);
  }

  async getUsersSP(req: Request): Promise<any> {
    return this.callStoredProcedure(req, 'get_users');
  }

  async getUserByIdSP(req: Request, id: number): Promise<any> {
    return this.callStoredProcedure(req, 'get_user_by_id', [id]);
  }

  async updateUserSP(req: Request, id: number, name?: string, email?: string, status?: boolean): Promise<any> {
    return this.callStoredProcedure(req, 'update_user', [id, name, email, status]);
  }

  async deleteUserSP(req: Request, id: number): Promise<any> {
    return this.callStoredProcedure(req, 'delete_user', [id]);
  }

  async benchmarkOperationsSP(req: Request): Promise<any> {
    return this.callStoredProcedure(req, 'benchmark_operations');
  }

  async onApplicationShutdown() {
    await Promise.all(
      Array.from(this.clientConnections.values()).map((conn) => conn.destroy()),
    );
    this.clientConnections.clear();
  }
}
