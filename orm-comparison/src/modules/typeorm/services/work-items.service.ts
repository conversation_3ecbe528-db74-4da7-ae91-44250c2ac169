import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { WorkItems } from '../entities/work_items.entity';
import { Request } from 'express';
import { TypeOrmService } from './typeorm.service';

export interface FetchItemsOptions {
  workItemColumnIds?: string[];
  jobIds?: number[];
  packageIds?: number[];
  drawingIds?: number[];
  stageIds?: number[];
  bom?: boolean;
  isAssigned?: boolean;
  includeArchived?: boolean;
  currentUser?: number;
  defaultRound?: number;
  groupHow?: 0 | 1 | 2 | 3;
  appType?: 'fab';
}

@Injectable()
export class WorkItemsService {
  constructor(
    private readonly typeOrmService: TypeOrmService,
  ) {}

  async fetchItems(req: Request, options: FetchItemsOptions) {
    const repository = await this.typeOrmService.getWorkItemsRepository(req);
    const queryBuilder = this.createBaseQuery(repository, options);

    this.applyFilters(queryBuilder, options);
    this.applyGrouping(queryBuilder, options);
    this.applyJoins(queryBuilder, options);

    // Add tenant context from middleware
    const tenantId = (req as any).tenantId;
    if (!tenantId) {
      throw new BadRequestException('Tenant ID is required');
    }

    // Apply tenant filter through job relationship
    queryBuilder.andWhere('job.tenantId = :tenantId', { tenantId });

    return queryBuilder.getMany();
  }

  private createBaseQuery(repository: Repository<WorkItems>, options: FetchItemsOptions): SelectQueryBuilder<WorkItems> {
    const { groupHow = 0 } = options;
    const qb = repository.createQueryBuilder('wi')
      .leftJoinAndSelect('wi.job', 'job')
      .leftJoinAndSelect('wi.package', 'pkg')
      .leftJoinAndSelect('wi.spool', 'spool')
      .where('wi.deleted = :deleted', { deleted: false });

    // Add selects based on grouping
    if (groupHow !== 0) {
      qb.addSelect('SUM(CASE WHEN mt.isCut = 1 THEN wi.length ELSE 1 END)', 'quantity');
    }

    return qb;
  }

  private applyFilters(qb: SelectQueryBuilder<WorkItems>, options: FetchItemsOptions) {
    const {
      jobIds,
      packageIds,
      drawingIds,
      stageIds,
      bom,
      includeArchived,
      appType = 'fab',
    } = options;

    if (jobIds?.length) {
      qb.andWhere('wi.jobId IN (:...jobIds)', { jobIds });
    }

    if (packageIds?.length) {
      qb.andWhere('wi.packageId IN (:...packageIds)', { packageIds });
    }

    if (drawingIds?.length) {
      qb.andWhere('wi.spoolId IN (:...drawingIds)', { drawingIds });
    }

    if (stageIds?.length) {
      qb.leftJoin('wi.stages', 'stages')
        .andWhere('stages.stageId IN (:...stageIds)', { stageIds });
    }

    if (bom) {
      qb.leftJoin('wi.materialType', 'mt')
        .andWhere('mt.isBomable = :isBomable', { isBomable: true });
    }

    if (!includeArchived) {
      qb.andWhere('wi.archived = :archived', { archived: false });
    }

    qb.andWhere(`wi.${appType} = :isEnabled`, { isEnabled: true });
  }

  private applyGrouping(qb: SelectQueryBuilder<WorkItems>, options: FetchItemsOptions) {
    const { groupHow = 0, workItemColumnIds = [] } = options;

    if (groupHow === 0) {
      qb.groupBy('wi.id');
      return;
    }

    if (groupHow === 1 || groupHow === 2) {
      if (!workItemColumnIds.length) {
        throw new BadRequestException('Column IDs are required for grouping');
      }

      const groupColumns = workItemColumnIds.map(col => `wi.${col}`);
      if (groupHow === 2) {
        groupColumns.unshift('wi.spoolId');
      }

      qb.groupBy(groupColumns.join(', '))
        .addGroupBy('wi.rejected');

    } else if (groupHow === 3) {
      qb.groupBy('wi.jobId, wi.packageId, wi.heatNumber, wi.vendor, wi.materialTypeId, wi.size');
    }
  }

  private applyJoins(qb: SelectQueryBuilder<WorkItems>, options: FetchItemsOptions) {
    const { isAssigned, currentUser } = options;

    if (isAssigned && currentUser) {
      qb.leftJoin('spool.assignments', 'sa', 'sa.userId = :currentUser', { currentUser })
        .leftJoin('pkg.assignments', 'pa', 'pa.userId = :currentUser', { currentUser })
        .leftJoin('job.assignments', 'ja', 'ja.userId = :currentUser', { currentUser })
        .andWhere('COALESCE(sa.id, pa.id, ja.id) IS NOT NULL');
    }
  }
}
