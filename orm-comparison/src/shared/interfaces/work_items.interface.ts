export interface IWorkItems {
  id: number;
  name: string;
  spoolId: number;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  // Optional fields from TypeORM entity
  revitItemId?: string;
  tagNumber?: string;
  endPrep1?: string;
  endPrep2?: string;
  endPrep3?: string;
  endPrep4?: string;
  length?: number;
  roundedCutLength?: number;
  stockLength?: number;
  identifier?: string;
  measurementArea?: string;
  height?: number;
  width?: number;
  thickness?: number;
  paintSpec?: string;
  texture?: string;
  fixtureType?: string;
  gauge?: string;
  weight?: number;
  hangerSize?: string;
  productCode?: string;
  insulation?: string;
  insulationArea?: number;
  insulationGauge?: string;
  insulationSpecification?: string;
  joiningProcedureId?: number;
  materialTypeId?: number;
  serviceName?: string;
  serviceColorName?: string;
  size?: string;
  area?: string;
  randomLength?: boolean;
  rodSize?: string;
  supportRodLength?: number;
  supportRodLength2?: number;
  laydownLocationId?: number;
  linerSpec?: string;
  heatNumber?: string;
  containerName?: string;
  archived?: boolean;
  fillerMetal?: string;
  vendor?: string;
  jobId?: number;
  packageId?: number;
  deleted?: boolean;
  fab?: boolean;
}
