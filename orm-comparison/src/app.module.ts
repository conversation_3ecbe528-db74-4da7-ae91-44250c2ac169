import { Mo<PERSON><PERSON>, MiddlewareConsumer, RequestMethod } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PrismaModule } from './modules/prisma/prisma.module';
import { TypeOrmUserModule } from './modules/typeorm/typeorm.module';
import { MikroOrmUserModule } from './modules/mikroorm/mikroorm.module';
import { StoredProcedureModule } from './modules/stored-procedures/stored-procedure.module';
import { BenchmarkModule } from './modules/benchmark/benchmark.module';
import { TenantMiddleware } from './common/middleware/tenant.middleware';
import { getDatabaseConfig } from './config/database.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot({
      type: 'mariadb', // Using MariaDB as specified
      host: getDatabaseConfig('devapi').host, // Use your actual config
      port: getDatabaseConfig('devapi').port, // Port 65107 as configured
      username: getDatabaseConfig('devapi').user,
      password: getDatabaseConfig('devapi').password,
      database: getDatabaseConfig('devapi').database, // Use devapi database
      entities: [__dirname + '/**/*.entity{.ts,.js}'],
      synchronize: false, // Disable schema sync for production database
      autoLoadEntities: true,
      logging: true, // Enable logging to see what's happening
    }),
    PrismaModule,
    TypeOrmUserModule,
    MikroOrmUserModule,
    StoredProcedureModule,
    BenchmarkModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(TenantMiddleware)
      .forRoutes({ path: '*', method: RequestMethod.ALL });
  }
}
