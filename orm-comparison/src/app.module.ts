import { Modu<PERSON>, MiddlewareConsumer, RequestMethod } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from './modules/database/database.module';
import { PrismaModule } from './modules/prisma/prisma.module';
import { TypeOrmUserModule } from './modules/typeorm/typeorm.module';
import { MikroOrmUserModule } from './modules/mikroorm/mikroorm.module';
import { StoredProcedureModule } from './modules/stored-procedures/stored-procedure.module';
import { BenchmarkModule } from './modules/benchmark/benchmark.module';
import { TenantMiddleware } from './common/middleware/tenant.middleware';
import { getDatabaseConfig } from './config/database.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    // TypeORM with SAFE configuration - NO schema synchronization
    TypeOrmModule.forRoot({
      type: 'mariadb',
      host: getDatabaseConfig('devapi').host,
      port: getDatabaseConfig('devapi').port,
      username: getDatabaseConfig('devapi').user,
      password: getDatabaseConfig('devapi').password,
      database: getDatabaseConfig('devapi').database,
      entities: [], // Empty entities to prevent schema operations
      synchronize: false, // NEVER synchronize production database
      autoLoadEntities: false, // Disable auto-loading
      logging: false, // Disable logging
      migrations: [], // No migrations
      migrationsRun: false, // Never run migrations
      dropSchema: false, // Never drop schema
    }),
    DatabaseModule, // Safe database service for raw queries
    PrismaModule, // Prisma ORM
    StoredProcedureModule, // Direct stored procedures
    // TypeOrmUserModule, // TypeORM implementation - temporarily disabled
    // MikroOrmUserModule, // MikroORM implementation - temporarily disabled
    // BenchmarkModule, // Performance comparison - temporarily disabled
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(TenantMiddleware)
      .forRoutes({ path: '*', method: RequestMethod.ALL });
  }
}
