export function Measure() {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const start = process.hrtime();
      const result = await originalMethod.apply(this, args);
      const end = process.hrtime(start);
      const executionTime = (end[0] * 1000000000 + end[1]) / 1000000; // Convert to milliseconds

      console.log(
        `[${propertyKey}] Execution time: ${executionTime.toFixed(2)}ms`,
      );
      return result;
    };

    return descriptor;
  };
}
