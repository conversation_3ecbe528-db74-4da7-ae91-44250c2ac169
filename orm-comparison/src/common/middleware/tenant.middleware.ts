import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { EnvironmentConfig } from '../../config/environment.config';

@Injectable()
export class TenantMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const tenantId = req.headers[EnvironmentConfig.TENANT_HEADER.toLowerCase()] as string;
    if (!tenantId) {
      res.status(400).json({ error: 'X-Tenant-Id header is required' });
      return;
    }
    (req as any).tenantId = tenantId;
    next();
  }
}
