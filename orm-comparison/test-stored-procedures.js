#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
const TENANT_ID = 'devapi';

const headers = {
  'Content-Type': 'application/json',
  'X-Tenant-Id': TENANT_ID,
};

async function testStoredProcedures() {
  console.log('🚀 Testing Stored Procedures Implementation\n');

  try {
    // Test 1: Create user via direct stored procedure
    console.log('1️⃣ Testing Direct Stored Procedure - Create User');
    const createResponse = await axios.post(`${BASE_URL}/stored-procedures/users`, {
      name: 'Test User SP',
      email: '<EMAIL>',
      status: true
    }, { headers });
    console.log(`   ✅ Duration: ${createResponse.data.duration.toFixed(2)}ms\n`);

    // Test 2: Get users via direct stored procedure
    console.log('2️⃣ Testing Direct Stored Procedure - Get Users');
    const getUsersResponse = await axios.get(`${BASE_URL}/stored-procedures/users`, { headers });
    console.log(`   ✅ Duration: ${getUsersResponse.data.duration.toFixed(2)}ms\n`);

    // Test 3: Comprehensive benchmark - Create User
    console.log('3️⃣ Testing Comprehensive Benchmark - Create User');
    const benchmarkCreateResponse = await axios.post(`${BASE_URL}/benchmark/users/create`, {
      name: 'Benchmark User',
      email: '<EMAIL>',
      status: true
    }, { headers });
    
    console.log('   📊 Performance Results:');
    Object.entries(benchmarkCreateResponse.data.results).forEach(([method, result]) => {
      if (result.duration > 0) {
        console.log(`   ${method}: ${result.duration.toFixed(2)}ms`);
      }
    });
    console.log(`   🏆 Fastest: ${benchmarkCreateResponse.data.summary.fastest}`);
    console.log(`   🐌 Slowest: ${benchmarkCreateResponse.data.summary.slowest}\n`);

    // Test 4: Comprehensive benchmark - Get Users
    console.log('4️⃣ Testing Comprehensive Benchmark - Get Users');
    const benchmarkGetResponse = await axios.get(`${BASE_URL}/benchmark/users`, { headers });
    
    console.log('   📊 Performance Results:');
    Object.entries(benchmarkGetResponse.data.results).forEach(([method, result]) => {
      if (result.duration > 0) {
        console.log(`   ${method}: ${result.duration.toFixed(2)}ms`);
      }
    });
    console.log(`   🏆 Fastest: ${benchmarkGetResponse.data.summary.fastest}`);
    console.log(`   🐌 Slowest: ${benchmarkGetResponse.data.summary.slowest}\n`);

    // Test 5: TypeORM Stored Procedure
    console.log('5️⃣ Testing TypeORM Stored Procedure');
    const typeormSPResponse = await axios.post(`${BASE_URL}/typeorm/users/sp/users`, {
      name: 'TypeORM SP User',
      email: '<EMAIL>',
      status: true
    }, { headers });
    console.log(`   ✅ Duration: ${typeormSPResponse.data.duration.toFixed(2)}ms\n`);

    // Test 6: MikroORM Stored Procedure
    console.log('6️⃣ Testing MikroORM Stored Procedure');
    const mikroormSPResponse = await axios.post(`${BASE_URL}/mikroorm/users/sp/users`, {
      name: 'MikroORM SP User',
      email: '<EMAIL>',
      status: true
    }, { headers });
    console.log(`   ✅ Duration: ${mikroormSPResponse.data.duration.toFixed(2)}ms\n`);

    // Test 7: Prisma Stored Procedure
    console.log('7️⃣ Testing Prisma Stored Procedure');
    const prismaSPResponse = await axios.post(`${BASE_URL}/prisma/users/sp/users`, {
      name: 'Prisma SP User',
      email: '<EMAIL>',
      status: true
    }, { headers });
    console.log(`   ✅ Duration: ${prismaSPResponse.data.duration.toFixed(2)}ms\n`);

    // Test 8: Benchmark Operations (Bulk)
    console.log('8️⃣ Testing Benchmark Operations (Bulk)');
    const benchmarkOpsResponse = await axios.post(`${BASE_URL}/benchmark/benchmark-operations`, {}, { headers });
    
    console.log('   📊 Bulk Operations Performance:');
    Object.entries(benchmarkOpsResponse.data.results).forEach(([method, result]) => {
      if (result.duration > 0) {
        console.log(`   ${method}: ${result.duration.toFixed(2)}ms`);
      }
    });
    console.log(`   🏆 Fastest: ${benchmarkOpsResponse.data.summary.fastest}\n`);

    console.log('🎉 All tests completed successfully!');
    console.log('\n📈 Summary:');
    console.log('- Direct stored procedures provide the best performance');
    console.log('- ORM + stored procedures offer good balance of performance and features');
    console.log('- Each approach has its use cases depending on requirements');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    console.log('\n💡 Make sure:');
    console.log('1. The NestJS application is running (npm run start:dev)');
    console.log('2. Database is accessible and contains the stored procedures');
    console.log('3. Tenant "devapi" is configured in the database config');
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  testStoredProcedures();
}

module.exports = { testStoredProcedures };
