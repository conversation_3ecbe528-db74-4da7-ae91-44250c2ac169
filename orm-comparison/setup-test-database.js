#!/usr/bin/env node

/**
 * Database Setup Script for Real Benchmark Testing
 * This script creates test data and verifies stored procedures
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'orm_comparison_test',
  multipleStatements: true
};

class DatabaseSetup {
  constructor() {
    this.connection = null;
  }

  async connect() {
    console.log('🔌 Connecting to database...');
    console.log(`📋 ${DB_CONFIG.host}:${DB_CONFIG.port}/${DB_CONFIG.database}`);
    
    try {
      this.connection = await mysql.createConnection(DB_CONFIG);
      await this.connection.ping();
      console.log('✅ Database connection successful\n');
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      throw error;
    }
  }

  async createTestTables() {
    console.log('📋 Creating test tables...');
    
    const createTablesSQL = `
      -- Create users table
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        status BOOLEAN DEFAULT TRUE,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      );

      -- Create jobs table
      CREATE TABLE IF NOT EXISTS jobs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        status VARCHAR(50) DEFAULT 'pending',
        tenantId VARCHAR(50) DEFAULT 'devapi',
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      );

      -- Create packages table
      CREATE TABLE IF NOT EXISTS packages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        jobId INT,
        status VARCHAR(50) DEFAULT 'pending',
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (jobId) REFERENCES jobs(id)
      );

      -- Create spools table
      CREATE TABLE IF NOT EXISTS spools (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        packageId INT,
        status VARCHAR(50) DEFAULT 'pending',
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (packageId) REFERENCES packages(id)
      );

      -- Create work_items table
      CREATE TABLE IF NOT EXISTS work_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        spoolId INT,
        jobId INT,
        packageId INT,
        status VARCHAR(50) DEFAULT 'pending',
        revitItemId VARCHAR(100),
        tagNumber VARCHAR(100),
        endPrep1 VARCHAR(50),
        endPrep2 VARCHAR(50),
        endPrep3 VARCHAR(50),
        endPrep4 VARCHAR(50),
        length DECIMAL(10,2),
        roundedCutLength DECIMAL(10,2),
        stockLength DECIMAL(10,2),
        identifier VARCHAR(100),
        measurementArea VARCHAR(100),
        height DECIMAL(10,2),
        width DECIMAL(10,2),
        thickness DECIMAL(10,2),
        paintSpec VARCHAR(100),
        texture VARCHAR(100),
        fixtureType VARCHAR(100),
        gauge VARCHAR(50),
        weight DECIMAL(10,2),
        hangerSize VARCHAR(50),
        productCode VARCHAR(100),
        insulation VARCHAR(100),
        insulationArea DECIMAL(10,2),
        insulationGauge VARCHAR(50),
        insulationSpecification VARCHAR(255),
        joiningProcedureId INT,
        materialTypeId INT,
        serviceName VARCHAR(100),
        serviceColorName VARCHAR(100),
        size VARCHAR(100),
        area VARCHAR(100),
        randomLength BOOLEAN DEFAULT FALSE,
        rodSize VARCHAR(50),
        supportRodLength DECIMAL(10,2),
        supportRodLength2 DECIMAL(10,2),
        laydownLocationId INT,
        linerSpec VARCHAR(100),
        heatNumber VARCHAR(100),
        containerName VARCHAR(100),
        archived BOOLEAN DEFAULT FALSE,
        fillerMetal VARCHAR(100),
        vendor VARCHAR(100),
        fab BOOLEAN DEFAULT TRUE,
        deleted BOOLEAN DEFAULT FALSE,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (spoolId) REFERENCES spools(id),
        FOREIGN KEY (jobId) REFERENCES jobs(id),
        FOREIGN KEY (packageId) REFERENCES packages(id)
      );

      -- Create work_item_stages table (for stage filtering)
      CREATE TABLE IF NOT EXISTS work_item_stages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        workItemId INT,
        stageId INT,
        status VARCHAR(50) DEFAULT 'pending',
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (workItemId) REFERENCES work_items(id)
      );

      -- Create work_item_assignments table (for assignment filtering)
      CREATE TABLE IF NOT EXISTS work_item_assignments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        workItemId INT,
        userId INT,
        assignedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (workItemId) REFERENCES work_items(id),
        FOREIGN KEY (userId) REFERENCES users(id)
      );
    `;

    try {
      await this.connection.execute(createTablesSQL);
      console.log('✅ Test tables created successfully\n');
    } catch (error) {
      console.error('❌ Failed to create tables:', error.message);
      throw error;
    }
  }

  async insertTestData() {
    console.log('📊 Inserting test data...');
    
    try {
      // Insert test users
      await this.connection.execute(`
        INSERT IGNORE INTO users (id, name, email, status) VALUES
        (1, 'Test User 1', '<EMAIL>', TRUE),
        (2, 'Test User 2', '<EMAIL>', TRUE),
        (3, 'Test User 3', '<EMAIL>', FALSE)
      `);

      // Insert test jobs
      await this.connection.execute(`
        INSERT IGNORE INTO jobs (id, name, status, tenantId) VALUES
        (1, 'Test Job 1', 'active', 'devapi'),
        (2, 'Test Job 2', 'active', 'devapi'),
        (3, 'Test Job 3', 'pending', 'devapi'),
        (4, 'Test Job 4', 'completed', 'devapi'),
        (5, 'Test Job 5', 'active', 'devapi')
      `);

      // Insert test packages
      await this.connection.execute(`
        INSERT IGNORE INTO packages (id, name, jobId, status) VALUES
        (10, 'Package 1-A', 1, 'active'),
        (11, 'Package 1-B', 1, 'active'),
        (20, 'Package 2-A', 2, 'active'),
        (21, 'Package 2-B', 2, 'pending'),
        (30, 'Package 3-A', 3, 'pending')
      `);

      // Insert test spools
      await this.connection.execute(`
        INSERT IGNORE INTO spools (id, name, packageId, status) VALUES
        (100, 'Spool 1-A-1', 10, 'active'),
        (101, 'Spool 1-A-2', 10, 'active'),
        (110, 'Spool 1-B-1', 11, 'active'),
        (200, 'Spool 2-A-1', 20, 'active'),
        (201, 'Spool 2-A-2', 20, 'pending'),
        (300, 'Spool 3-A-1', 30, 'pending')
      `);

      // Insert test work items (more realistic data)
      const workItemsSQL = `
        INSERT IGNORE INTO work_items (
          id, name, spoolId, jobId, packageId, status, revitItemId, tagNumber,
          length, weight, archived, fab, deleted
        ) VALUES
      `;

      const workItemsData = [];
      for (let i = 1; i <= 1000; i++) {
        const spoolId = [100, 101, 110, 200, 201, 300][i % 6];
        const jobId = Math.floor((i - 1) / 200) + 1;
        const packageId = spoolId < 200 ? (spoolId < 110 ? 10 : 11) : (spoolId < 300 ? 20 : 30);
        
        workItemsData.push(`(
          ${i}, 
          'Work Item ${i}', 
          ${spoolId}, 
          ${jobId}, 
          ${packageId}, 
          '${['pending', 'active', 'completed'][i % 3]}',
          'REVIT_${i}',
          'TAG_${i}',
          ${(Math.random() * 100 + 10).toFixed(2)},
          ${(Math.random() * 50 + 5).toFixed(2)},
          ${i % 50 === 0 ? 'TRUE' : 'FALSE'},
          TRUE,
          FALSE
        )`);
      }

      await this.connection.execute(workItemsSQL + workItemsData.join(','));

      // Insert test stages
      await this.connection.execute(`
        INSERT IGNORE INTO work_item_stages (workItemId, stageId, status) 
        SELECT id, (id % 10) + 1, 'active' FROM work_items WHERE id <= 100
      `);

      // Insert test assignments
      await this.connection.execute(`
        INSERT IGNORE INTO work_item_assignments (workItemId, userId) 
        SELECT id, (id % 3) + 1 FROM work_items WHERE id <= 200
      `);

      console.log('✅ Test data inserted successfully');
      
      // Show data counts
      const [jobCount] = await this.connection.execute('SELECT COUNT(*) as count FROM jobs');
      const [packageCount] = await this.connection.execute('SELECT COUNT(*) as count FROM packages');
      const [spoolCount] = await this.connection.execute('SELECT COUNT(*) as count FROM spools');
      const [workItemCount] = await this.connection.execute('SELECT COUNT(*) as count FROM work_items');
      
      console.log(`   📊 Jobs: ${jobCount[0].count}`);
      console.log(`   📊 Packages: ${packageCount[0].count}`);
      console.log(`   📊 Spools: ${spoolCount[0].count}`);
      console.log(`   📊 Work Items: ${workItemCount[0].count}\n`);
      
    } catch (error) {
      console.error('❌ Failed to insert test data:', error.message);
      throw error;
    }
  }

  async createBasicStoredProcedures() {
    console.log('🔧 Creating basic stored procedures...');
    
    const procedures = [
      {
        name: 'create_user',
        sql: `
          CREATE PROCEDURE create_user(
            IN p_name VARCHAR(255),
            IN p_email VARCHAR(255),
            IN p_status BOOLEAN
          )
          BEGIN
            INSERT INTO users (name, email, status) VALUES (p_name, p_email, p_status);
            SELECT LAST_INSERT_ID() as id, p_name as name, p_email as email, p_status as status;
          END
        `
      },
      {
        name: 'get_users',
        sql: `
          CREATE PROCEDURE get_users()
          BEGIN
            SELECT * FROM users ORDER BY id;
          END
        `
      },
      {
        name: 'get_user_by_id',
        sql: `
          CREATE PROCEDURE get_user_by_id(IN p_id INT)
          BEGIN
            SELECT * FROM users WHERE id = p_id;
          END
        `
      },
      {
        name: 'update_user',
        sql: `
          CREATE PROCEDURE update_user(
            IN p_id INT,
            IN p_name VARCHAR(255),
            IN p_email VARCHAR(255),
            IN p_status BOOLEAN
          )
          BEGIN
            UPDATE users 
            SET name = COALESCE(p_name, name),
                email = COALESCE(p_email, email),
                status = COALESCE(p_status, status),
                updatedAt = CURRENT_TIMESTAMP
            WHERE id = p_id;
            SELECT * FROM users WHERE id = p_id;
          END
        `
      },
      {
        name: 'delete_user',
        sql: `
          CREATE PROCEDURE delete_user(IN p_id INT)
          BEGIN
            DELETE FROM users WHERE id = p_id;
            SELECT ROW_COUNT() as affected_rows;
          END
        `
      }
    ];

    for (const proc of procedures) {
      try {
        await this.connection.execute(`DROP PROCEDURE IF EXISTS ${proc.name}`);
        await this.connection.execute(proc.sql);
        console.log(`   ✅ Created ${proc.name}`);
      } catch (error) {
        console.log(`   ⚠️  Failed to create ${proc.name}: ${error.message}`);
      }
    }
    
    console.log('');
  }

  async verifySpFetchItems() {
    console.log('🔍 Verifying sp_fetch_items stored procedure...');
    
    try {
      const [procedures] = await this.connection.execute(
        "SELECT ROUTINE_NAME FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_SCHEMA = ? AND ROUTINE_NAME = 'sp_fetch_items'",
        [DB_CONFIG.database]
      );
      
      if (procedures.length === 0) {
        console.log('⚠️  sp_fetch_items not found. Please run the sp_fetch_items.sql script:');
        console.log('   mysql -u username -p database_name < sp_fetch_items.sql');
        return false;
      }
      
      // Test the procedure with basic parameters
      const [results] = await this.connection.execute(
        'CALL sp_fetch_items(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [null, null, null, null, null, 0, 0, 0, null, 1, 0, 'fab']
      );
      
      console.log(`✅ sp_fetch_items verified (returned ${Array.isArray(results) ? results.length : 0} rows)\n`);
      return true;
      
    } catch (error) {
      console.log(`❌ sp_fetch_items verification failed: ${error.message}\n`);
      return false;
    }
  }

  async createIndexes() {
    console.log('📈 Creating performance indexes...');
    
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_work_items_job_id ON work_items(jobId)',
      'CREATE INDEX IF NOT EXISTS idx_work_items_package_id ON work_items(packageId)',
      'CREATE INDEX IF NOT EXISTS idx_work_items_spool_id ON work_items(spoolId)',
      'CREATE INDEX IF NOT EXISTS idx_work_items_status ON work_items(status)',
      'CREATE INDEX IF NOT EXISTS idx_work_items_archived ON work_items(archived)',
      'CREATE INDEX IF NOT EXISTS idx_work_items_deleted ON work_items(deleted)',
      'CREATE INDEX IF NOT EXISTS idx_work_item_stages_work_item_id ON work_item_stages(workItemId)',
      'CREATE INDEX IF NOT EXISTS idx_work_item_stages_stage_id ON work_item_stages(stageId)',
      'CREATE INDEX IF NOT EXISTS idx_work_item_assignments_work_item_id ON work_item_assignments(workItemId)',
      'CREATE INDEX IF NOT EXISTS idx_work_item_assignments_user_id ON work_item_assignments(userId)'
    ];

    for (const indexSQL of indexes) {
      try {
        await this.connection.execute(indexSQL);
        console.log(`   ✅ Index created`);
      } catch (error) {
        console.log(`   ⚠️  Index creation failed: ${error.message}`);
      }
    }
    
    console.log('');
  }

  async cleanup() {
    if (this.connection) {
      await this.connection.end();
      console.log('✅ Database connection closed');
    }
  }

  async setup() {
    try {
      await this.connect();
      await this.createTestTables();
      await this.insertTestData();
      await this.createBasicStoredProcedures();
      await this.createIndexes();
      const spVerified = await this.verifySpFetchItems();
      
      console.log('🎉 Database setup complete!');
      console.log('');
      console.log('Next steps:');
      console.log('1. Run real benchmark test: node real-benchmark-test.js');
      if (!spVerified) {
        console.log('2. Install sp_fetch_items: mysql -u user -p database < sp_fetch_items.sql');
      }
      console.log('');
      
    } catch (error) {
      console.error('❌ Setup failed:', error.message);
      throw error;
    } finally {
      await this.cleanup();
    }
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help')) {
    console.log('Database Setup for Real Benchmark Testing');
    console.log('');
    console.log('Usage:');
    console.log('  node setup-test-database.js           # Setup test database');
    console.log('  node setup-test-database.js --help    # Show this help');
    console.log('');
    console.log('Environment Variables:');
    console.log('  DB_HOST=localhost        # Database host');
    console.log('  DB_PORT=3306            # Database port');
    console.log('  DB_USER=root            # Database user');
    console.log('  DB_PASSWORD=password    # Database password');
    console.log('  DB_NAME=test_db         # Database name');
    return;
  }

  const setup = new DatabaseSetup();
  await setup.setup();
}

if (require.main === module) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { DatabaseSetup };
