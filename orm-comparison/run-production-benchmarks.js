#!/usr/bin/env node

/**
 * Production Benchmark Runner
 * Builds the TypeScript config and runs benchmarks on actual production databases
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class ProductionBenchmarkRunner {
  constructor() {
    this.results = null;
    this.startTime = new Date();
    this.availableTenants = ['devapi', 'staging3_dev'];
  }

  async checkPrerequisites() {
    console.log('🔍 Checking Prerequisites...');
    
    // Check if required packages are installed
    const requiredPackages = ['mysql2', '@prisma/client'];
    for (const pkg of requiredPackages) {
      try {
        require(pkg);
        console.log(`✅ ${pkg} package found`);
      } catch (error) {
        throw new Error(`${pkg} package not found. Run: npm install ${pkg}`);
      }
    }

    // Check if TypeScript config exists
    if (!fs.existsSync('tsconfig.json')) {
      throw new Error('tsconfig.json not found. This should be a NestJS project.');
    }

    // Check if database config exists
    if (!fs.existsSync('src/config/database.config.ts')) {
      throw new Error('Database configuration not found at src/config/database.config.ts');
    }

    console.log('✅ Prerequisites check complete\n');
  }

  async buildTypeScriptConfig() {
    console.log('🔧 Building TypeScript configuration...');
    
    return new Promise((resolve, reject) => {
      const buildProcess = spawn('npm', ['run', 'build'], {
        stdio: 'pipe'
      });

      let output = '';
      let errorOutput = '';

      buildProcess.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        // Only show important build messages
        if (text.includes('error') || text.includes('warning') || text.includes('Successfully compiled')) {
          process.stdout.write(text);
        }
      });

      buildProcess.stderr.on('data', (data) => {
        const text = data.toString();
        errorOutput += text;
        process.stderr.write(text);
      });

      buildProcess.on('close', (code) => {
        if (code === 0) {
          console.log('✅ TypeScript build completed\n');
          resolve();
        } else {
          reject(new Error(`TypeScript build failed with code ${code}\n${errorOutput}`));
        }
      });
    });
  }

  async listAvailableTenants() {
    console.log('📋 Available Database Tenants:');
    
    try {
      // Import the built configuration
      const { getDatabaseConfig } = require('./dist/config/database.config');
      
      for (const tenant of this.availableTenants) {
        try {
          const config = getDatabaseConfig(tenant);
          console.log(`  ✅ ${tenant.padEnd(15)}: ${config.host}:${config.port}/${config.database}`);
        } catch (error) {
          console.log(`  ❌ ${tenant.padEnd(15)}: Configuration error - ${error.message}`);
        }
      }
    } catch (error) {
      console.log('  ❌ Could not load database configuration. Make sure TypeScript is built.');
    }
    
    console.log('');
  }

  async runProductionBenchmarks(tenantId) {
    console.log(`🚀 Running production benchmarks for tenant: ${tenantId}`);
    
    return new Promise((resolve, reject) => {
      const benchmarkProcess = spawn('node', ['production-benchmark-test.js', tenantId], {
        stdio: 'pipe'
      });

      let output = '';
      let errorOutput = '';

      benchmarkProcess.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        process.stdout.write(text);
      });

      benchmarkProcess.stderr.on('data', (data) => {
        const text = data.toString();
        errorOutput += text;
        process.stderr.write(text);
      });

      benchmarkProcess.on('close', (code) => {
        if (code === 0) {
          console.log('✅ Production benchmark tests completed\n');
          
          // Try to find the results file
          const files = fs.readdirSync('.')
            .filter(f => f.startsWith(`production-benchmark-results-${tenantId}-`) && f.endsWith('.json'))
            .sort()
            .reverse();
          
          if (files.length > 0) {
            try {
              this.results = JSON.parse(fs.readFileSync(files[0], 'utf8'));
              console.log(`📄 Results loaded from: ${files[0]}\n`);
            } catch (error) {
              console.log('⚠️  Could not load results file\n');
            }
          }
          
          resolve();
        } else {
          reject(new Error(`Production benchmark tests failed with code ${code}\n${errorOutput}`));
        }
      });
    });
  }

  generateExecutiveSummary() {
    console.log('📊 EXECUTIVE SUMMARY - PRODUCTION BENCHMARK RESULTS');
    console.log('=' .repeat(80));
    
    if (!this.results) {
      console.log('❌ No results data available');
      return;
    }

    const { tenant, database, dataAnalysis, overallStats, scenarios } = this.results;
    
    console.log(`🏢 Tenant: ${tenant}`);
    console.log(`🗄️  Database: ${database.host}:${database.port}/${database.database}`);
    console.log(`📊 Data Volume: ${dataAnalysis.workItems?.toLocaleString() || 'Unknown'} work items`);
    console.log(`⏱️  Test Completed: ${new Date(this.results.endTime).toLocaleString()}`);
    console.log(`⏱️  Total Duration: ${this.results.duration.toFixed(1)} seconds`);
    console.log('');

    // Production data context
    if (dataAnalysis) {
      console.log('📈 PRODUCTION DATA CONTEXT:');
      console.log(`   Work Items: ${dataAnalysis.workItems?.toLocaleString() || 'N/A'}`);
      console.log(`   Jobs: ${dataAnalysis.jobs?.toLocaleString() || 'N/A'}`);
      console.log(`   Packages: ${dataAnalysis.packages?.toLocaleString() || 'N/A'}`);
      console.log(`   Spools: ${dataAnalysis.spools?.toLocaleString() || 'N/A'}`);
      console.log(`   Archived Items: ${dataAnalysis.archived?.toLocaleString() || 'N/A'}`);
      console.log(`   Assigned Items: ${dataAnalysis.assigned?.toLocaleString() || 'N/A'}`);
      
      if (dataAnalysis.statusDistribution) {
        console.log('   Status Distribution:');
        dataAnalysis.statusDistribution.forEach(status => {
          console.log(`      ${status.status}: ${status.count.toLocaleString()}`);
        });
      }
      console.log('');
    }

    // Overall performance ranking
    if (overallStats) {
      console.log('🏆 OVERALL PERFORMANCE RANKING (Production Data):');
      Object.entries(overallStats)
        .sort((a, b) => a[1].average - b[1].average)
        .forEach(([method, stats], index) => {
          const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '📍';
          console.log(`${medal} ${(index + 1)}. ${method.padEnd(15)}: ${stats.average.toFixed(2)}ms average`);
        });
      console.log('');
    }

    // Key insights
    this.generateProductionInsights();
    
    // Use case performance
    console.log('🎯 USE CASE PERFORMANCE:');
    scenarios.forEach((scenario, index) => {
      if (scenario.summary && scenario.summary.fastest) {
        console.log(`${index + 1}. ${scenario.scenario}`);
        console.log(`   Use Case: ${scenario.useCase}`);
        console.log(`   Best Performance: ${scenario.summary.fastest} (${scenario.summary.fastestTime.toFixed(2)}ms)`);
        console.log(`   Data Returned: ${scenario.summary.dataVolume?.toLocaleString() || 'N/A'} rows`);
        console.log('');
      }
    });
  }

  generateProductionInsights() {
    if (!this.results || !this.results.overallStats) return;

    console.log('💡 PRODUCTION PERFORMANCE INSIGHTS:');
    
    const stats = this.results.overallStats;
    const methods = Object.keys(stats).sort((a, b) => stats[a].average - stats[b].average);
    
    if (methods.length >= 2) {
      const fastest = methods[0];
      const slowest = methods[methods.length - 1];
      const speedup = (stats[slowest].average / stats[fastest].average).toFixed(1);
      
      console.log(`1. ${fastest} is ${speedup}x faster than ${slowest} on production data`);
    }

    // Calculate overhead percentages
    if (stats.direct_sp) {
      const baseline = stats.direct_sp.average;
      Object.entries(stats).forEach(([method, methodStats]) => {
        if (method !== 'direct_sp') {
          const overhead = ((methodStats.average - baseline) / baseline * 100).toFixed(1);
          console.log(`2. ${method} adds ${overhead}% overhead vs direct stored procedures`);
        }
      });
    }

    // Data volume insights
    if (this.results.dataAnalysis && this.results.dataAnalysis.workItems) {
      const dataSize = this.results.dataAnalysis.workItems;
      if (dataSize > 100000) {
        console.log(`3. Large dataset (${dataSize.toLocaleString()} items) - performance differences are significant`);
      } else if (dataSize > 10000) {
        console.log(`3. Medium dataset (${dataSize.toLocaleString()} items) - good for realistic testing`);
      } else {
        console.log(`3. Small dataset (${dataSize.toLocaleString()} items) - consider testing with more data`);
      }
    }

    console.log('');
    
    // Production recommendations
    console.log('🎯 PRODUCTION RECOMMENDATIONS:');
    
    if (stats.direct_sp) {
      console.log('✅ Direct stored procedures provide best performance for production workloads');
    }
    
    if (stats.prisma_sp && stats.direct_sp) {
      const prismaOverhead = ((stats.prisma_sp.average - stats.direct_sp.average) / stats.direct_sp.average * 100);
      if (prismaOverhead < 30) {
        console.log('✅ Prisma offers excellent production performance with great developer experience');
      } else if (prismaOverhead < 50) {
        console.log('✅ Prisma offers good production performance - acceptable for most use cases');
      } else {
        console.log('⚠️  Prisma has significant overhead - consider for non-critical paths only');
      }
    }
    
    console.log('✅ Implement performance monitoring for production queries');
    console.log('✅ Consider caching for frequently accessed data');
    console.log('✅ Monitor query performance during peak usage times');
    console.log('');
  }

  async generateProductionMarkdownReport() {
    if (!this.results) return;

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `PRODUCTION_BENCHMARK_REPORT_${this.results.tenant}_${timestamp}.md`;
    
    let markdown = `# Production SP_FETCH_ITEMS Benchmark Report

**Tenant**: ${this.results.tenant}  
**Database**: ${this.results.database.host}:${this.results.database.port}/${this.results.database.database}  
**Generated**: ${new Date().toLocaleString()}  
**Test Duration**: ${this.results.duration.toFixed(1)} seconds  

## Executive Summary

This report contains **REAL performance data** from actual production database execution of the sp_fetch_items stored procedure across different ORM implementations.

### Production Data Context
- **Work Items**: ${this.results.dataAnalysis.workItems?.toLocaleString() || 'N/A'}
- **Jobs**: ${this.results.dataAnalysis.jobs?.toLocaleString() || 'N/A'}
- **Packages**: ${this.results.dataAnalysis.packages?.toLocaleString() || 'N/A'}
- **Spools**: ${this.results.dataAnalysis.spools?.toLocaleString() || 'N/A'}
- **Archived Items**: ${this.results.dataAnalysis.archived?.toLocaleString() || 'N/A'}
- **Assigned Items**: ${this.results.dataAnalysis.assigned?.toLocaleString() || 'N/A'}

## Overall Performance Results (Production Data)

| Rank | Method | Average Time | Performance vs Fastest |
|------|--------|--------------|------------------------|
`;

    if (this.results.overallStats) {
      Object.entries(this.results.overallStats)
        .sort((a, b) => a[1].average - b[1].average)
        .forEach(([method, stats], index) => {
          const fastest = Object.values(this.results.overallStats).sort((a, b) => a.average - b.average)[0];
          const ratio = (stats.average / fastest.average).toFixed(2);
          const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '📍';
          markdown += `| ${medal} ${index + 1} | ${method} | ${stats.average.toFixed(2)}ms | ${ratio}x |\n`;
        });
    }

    markdown += `\n## Production Use Case Performance\n\n`;

    this.results.scenarios.forEach((scenario, index) => {
      markdown += `### ${index + 1}. ${scenario.scenario}\n\n`;
      markdown += `**Use Case**: ${scenario.useCase}\n`;
      markdown += `**Description**: ${scenario.description}\n\n`;
      
      if (scenario.results) {
        markdown += `| Method | Average | Min | Max | Std Dev | Success Rate | Avg Rows |\n`;
        markdown += `|--------|---------|-----|-----|---------|--------------|----------|\n`;
        
        Object.entries(scenario.results).forEach(([method, result]) => {
          if (result.average !== null) {
            markdown += `| ${method} | ${result.average.toFixed(2)}ms | ${result.min.toFixed(2)}ms | ${result.max.toFixed(2)}ms | ${result.standardDeviation?.toFixed(2) || 'N/A'}ms | ${result.successRate.toFixed(1)}% | ${result.avgRowCount?.toLocaleString() || 'N/A'} |\n`;
          } else {
            markdown += `| ${method} | Failed | - | - | - | 0% | - |\n`;
          }
        });
        
        markdown += `\n`;
      }
    });

    markdown += `\n## Key Findings\n\n`;
    markdown += `- **Real production data** with ${this.results.dataAnalysis.workItems?.toLocaleString() || 'N/A'} work items\n`;
    markdown += `- **Actual database execution** on ${this.results.database.host}:${this.results.database.port}\n`;
    markdown += `- **Statistical accuracy** with multiple iterations per test\n`;
    markdown += `- **Production-ready** performance characteristics\n`;
    markdown += `- **Real-world use cases** tested with actual query patterns\n\n`;

    markdown += `## Production Recommendations\n\n`;
    markdown += `Based on actual production data performance:\n\n`;
    
    if (this.results.overallStats.direct_sp) {
      markdown += `1. **Direct stored procedures** provide optimal performance for production workloads\n`;
    }
    
    if (this.results.overallStats.prisma_sp) {
      const overhead = ((this.results.overallStats.prisma_sp.average - this.results.overallStats.direct_sp.average) / this.results.overallStats.direct_sp.average * 100);
      markdown += `2. **Prisma** adds ${overhead.toFixed(1)}% overhead but offers excellent developer experience\n`;
    }
    
    markdown += `3. **Implement monitoring** for production query performance\n`;
    markdown += `4. **Consider caching** for frequently accessed data\n`;
    markdown += `5. **Test with peak load** to validate performance under stress\n\n`;

    markdown += `## Raw Data\n\n`;
    markdown += `Complete test results are available in: \`production-benchmark-results-${this.results.tenant}-${this.results.timestamp.replace(/[:.]/g, '-')}.json\`\n`;

    fs.writeFileSync(filename, markdown);
    console.log(`📄 Production markdown report generated: ${filename}`);
  }

  async run() {
    const args = process.argv.slice(2);
    
    if (args.includes('--help')) {
      console.log('Production Benchmark Runner');
      console.log('Runs REAL performance tests on your actual production databases');
      console.log('');
      console.log('Usage:');
      console.log('  node run-production-benchmarks.js [tenant]     # Run benchmarks');
      console.log('  node run-production-benchmarks.js --help       # Show this help');
      console.log('  node run-production-benchmarks.js --list       # List available tenants');
      console.log('');
      console.log('Examples:');
      console.log('  node run-production-benchmarks.js devapi       # Test devapi database');
      console.log('  node run-production-benchmarks.js staging3_dev # Test staging3_dev database');
      return;
    }

    if (args.includes('--list')) {
      try {
        await this.checkPrerequisites();
        await this.buildTypeScriptConfig();
        await this.listAvailableTenants();
      } catch (error) {
        console.error('❌ Failed to list tenants:', error.message);
      }
      return;
    }

    const tenantId = args[0] || 'devapi';

    console.log('🚀 Production Benchmark Runner');
    console.log('=' .repeat(50));
    console.log('This will run ACTUAL performance tests on PRODUCTION data\n');

    try {
      await this.checkPrerequisites();
      await this.buildTypeScriptConfig();
      await this.listAvailableTenants();
      await this.runProductionBenchmarks(tenantId);
      this.generateExecutiveSummary();
      await this.generateProductionMarkdownReport();
      
      console.log('🎉 Production benchmark testing completed successfully!');
      console.log('');
      console.log('📁 Generated Files:');
      console.log('   - JSON results file with raw production data');
      console.log('   - Markdown report with executive summary');
      console.log('');
      console.log('🔍 Next Steps:');
      console.log('   1. Review the production performance results');
      console.log('   2. Compare with your performance requirements');
      console.log('   3. Implement the best-performing approach');
      console.log('   4. Set up production performance monitoring');
      
    } catch (error) {
      console.error('❌ Production benchmark testing failed:', error.message);
      console.log('');
      console.log('💡 Troubleshooting:');
      console.log('1. Ensure your database is accessible');
      console.log('2. Check database.config.ts has correct credentials');
      console.log('3. Verify sp_fetch_items stored procedure exists');
      console.log('4. Ensure the database contains work_items data');
      console.log('5. Check database user has proper permissions');
      
      process.exit(1);
    }
  }
}

// CLI interface
async function main() {
  const runner = new ProductionBenchmarkRunner();
  await runner.run();
}

if (require.main === module) {
  main();
}

module.exports = { ProductionBenchmarkRunner };
