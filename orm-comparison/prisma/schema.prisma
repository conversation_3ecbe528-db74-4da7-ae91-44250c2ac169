// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
  // NOTE: Prisma doesn't support dynamic database switching at runtime
  // We'll handle this in the service layer
}

model User {
  id        Int      @id @default(autoincrement())
  name      String
  email     String   @unique
  status    Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

model Jobs {
  id        Int      @id @default(autoincrement())
  name      String
  status    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  // Add relations as needed
  @@map("jobs")
}

model Packages {
  id        Int      @id @default(autoincrement())
  name      String
  jobId     Int
  status    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  // Add relations as needed
  @@map("packages")
}

model Spools {
  id        Int      @id @default(autoincrement())
  name      String
  packageId Int
  status    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  // Add relations as needed
  @@map("spools")
}

model WorkItems {
  id        Int      @id @default(autoincrement())
  name      String
  spoolId   Int
  status    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  // Add relations as needed
  @@map("work_items")
}
