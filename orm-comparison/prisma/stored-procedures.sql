DELIMITER //

CREATE PROCEDURE create_user(
    IN p_name VARCHAR(255),
    IN p_email VARCHAR(255),
    IN p_status BOOLEAN
)
BEGIN
    INSERT INTO users (name, email, status, createdAt, updatedAt)
    VALUES (p_name, p_email, p_status, NOW(), NOW());
    SELECT LAST_INSERT_ID() as id;
END //

CREATE PROCEDURE get_users()
BEGIN
    SELECT * FROM users;
END //

CREATE PROCEDURE get_user_by_id(
    IN p_id INT
)
BEGIN
    SELECT * FROM users WHERE id = p_id;
END //

CREATE PROCEDURE update_user(
    IN p_id INT,
    IN p_name VARCHAR(255),
    IN p_email VARCHAR(255),
    IN p_status BOOLEAN
)
BEGIN
    UPDATE users
    SET name = COALESCE(p_name, name),
        email = COALESCE(p_email, email),
        status = COALESCE(p_status, status),
        updatedAt = NOW()
    WHERE id = p_id;
    SELECT * FROM users WHERE id = p_id;
END //

CREATE PROCEDURE delete_user(
    IN p_id INT
)
BEGIN
    DELETE FROM users WHERE id = p_id;
END //

CREATE PROCEDURE benchmark_operations()
BEGIN
    DECLARE start_time TIMESTAMP;
    DECLARE end_time TIMESTAMP;
    DECLARE i INT DEFAULT 0;
    
    SET start_time = NOW();
    
    -- Create 100 users
    WHILE i < 100 DO
        INSERT INTO users (name, email, status, createdAt, updatedAt)
        VALUES (CONCAT('User ', i), CONCAT('user', i, '@example.com'), TRUE, NOW(), NOW());
        SET i = i + 1;
    END WHILE;
    
    -- Read all users
    SELECT * FROM users;
    
    -- Update all users
    UPDATE users SET status = FALSE;
    
    -- Delete all users
    DELETE FROM users;
    
    SET end_time = NOW();
    
    SELECT TIMESTAMPDIFF(MICROSECOND, start_time, end_time) / 1000.0 as duration_ms;
END //

DELIMITER ;
