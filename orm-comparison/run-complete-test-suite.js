#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * Complete test suite runner for sp_fetch_items performance testing
 * This script orchestrates the entire testing workflow
 */

class TestSuiteRunner {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      tests: [],
      summary: {},
      environment: {}
    };
  }

  async runCompleteTestSuite() {
    console.log('🚀 Starting Complete SP_FETCH_ITEMS Test Suite');
    console.log('=' .repeat(60));

    try {
      // Step 1: Environment check
      await this.checkEnvironment();
      
      // Step 2: Start application if not running
      await this.ensureApplicationRunning();
      
      // Step 3: Run basic connectivity test
      await this.testConnectivity();
      
      // Step 4: Run sp_fetch_items tests
      await this.runSpFetchItemsTests();
      
      // Step 5: Run stored procedure comparison tests
      await this.runStoredProcedureTests();
      
      // Step 6: Generate performance dashboard
      await this.generateDashboard();
      
      // Step 7: Generate summary report
      await this.generateSummaryReport();
      
      console.log('✅ Complete test suite finished successfully!');
      
    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
      process.exit(1);
    }
  }

  async checkEnvironment() {
    console.log('\n📋 Checking Environment...');
    
    // Check Node version
    const nodeVersion = process.version;
    console.log(`   Node.js version: ${nodeVersion}`);
    this.results.environment.nodeVersion = nodeVersion;
    
    // Check if required files exist
    const requiredFiles = [
      'package.json',
      'src/main.ts',
      'test-sp-fetch-items.js',
      'test-stored-procedures.js'
    ];
    
    for (const file of requiredFiles) {
      if (fs.existsSync(file)) {
        console.log(`   ✅ ${file} exists`);
      } else {
        throw new Error(`Required file missing: ${file}`);
      }
    }
    
    // Check dependencies
    try {
      require('axios');
      console.log('   ✅ axios dependency available');
    } catch (error) {
      throw new Error('axios dependency missing. Run: npm install axios');
    }
  }

  async ensureApplicationRunning() {
    console.log('\n🔧 Checking Application Status...');
    
    return new Promise((resolve, reject) => {
      const axios = require('axios');
      
      // Test if application is running
      axios.get('http://localhost:3000')
        .then(() => {
          console.log('   ✅ Application is running');
          resolve();
        })
        .catch(() => {
          console.log('   ⚠️  Application not running, attempting to start...');
          
          // Try to start the application
          const appProcess = spawn('npm', ['run', 'start:dev'], {
            stdio: 'pipe',
            detached: true
          });
          
          let startupTimeout = setTimeout(() => {
            appProcess.kill();
            reject(new Error('Application failed to start within 30 seconds'));
          }, 30000);
          
          // Wait for application to be ready
          const checkReady = setInterval(() => {
            axios.get('http://localhost:3000')
              .then(() => {
                clearTimeout(startupTimeout);
                clearInterval(checkReady);
                console.log('   ✅ Application started successfully');
                resolve();
              })
              .catch(() => {
                // Still waiting
              });
          }, 2000);
        });
    });
  }

  async testConnectivity() {
    console.log('\n🔗 Testing Basic Connectivity...');
    
    const axios = require('axios');
    const headers = {
      'Content-Type': 'application/json',
      'X-Tenant-Id': 'devapi'
    };
    
    try {
      // Test basic endpoint
      const response = await axios.get('http://localhost:3000', { headers });
      console.log('   ✅ Basic connectivity successful');
      
      // Test tenant middleware
      const tenantResponse = await axios.get('http://localhost:3000/typeorm/users', { headers });
      console.log('   ✅ Tenant middleware working');
      
    } catch (error) {
      throw new Error(`Connectivity test failed: ${error.message}`);
    }
  }

  async runSpFetchItemsTests() {
    console.log('\n🔍 Running SP_FETCH_ITEMS Tests...');
    
    return new Promise((resolve, reject) => {
      const testProcess = spawn('node', ['test-sp-fetch-items.js'], {
        stdio: 'pipe'
      });
      
      let output = '';
      let errorOutput = '';
      
      testProcess.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        process.stdout.write(text);
      });
      
      testProcess.stderr.on('data', (data) => {
        const text = data.toString();
        errorOutput += text;
        process.stderr.write(text);
      });
      
      testProcess.on('close', (code) => {
        if (code === 0) {
          console.log('   ✅ SP_FETCH_ITEMS tests completed');
          this.results.tests.push({
            name: 'sp_fetch_items',
            status: 'success',
            output: output
          });
          resolve();
        } else {
          this.results.tests.push({
            name: 'sp_fetch_items',
            status: 'failed',
            output: output,
            error: errorOutput
          });
          reject(new Error(`SP_FETCH_ITEMS tests failed with code ${code}`));
        }
      });
    });
  }

  async runStoredProcedureTests() {
    console.log('\n📊 Running Stored Procedure Comparison Tests...');
    
    return new Promise((resolve, reject) => {
      const testProcess = spawn('node', ['test-stored-procedures.js'], {
        stdio: 'pipe'
      });
      
      let output = '';
      let errorOutput = '';
      
      testProcess.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        process.stdout.write(text);
      });
      
      testProcess.stderr.on('data', (data) => {
        const text = data.toString();
        errorOutput += text;
        process.stderr.write(text);
      });
      
      testProcess.on('close', (code) => {
        if (code === 0) {
          console.log('   ✅ Stored procedure tests completed');
          this.results.tests.push({
            name: 'stored_procedures',
            status: 'success',
            output: output
          });
          resolve();
        } else {
          this.results.tests.push({
            name: 'stored_procedures',
            status: 'failed',
            output: output,
            error: errorOutput
          });
          // Don't fail the entire suite for this
          console.log('   ⚠️  Stored procedure tests had issues, continuing...');
          resolve();
        }
      });
    });
  }

  async generateDashboard() {
    console.log('\n📊 Generating Performance Dashboard...');
    
    try {
      const { generatePerformanceDashboard } = require('./generate-performance-dashboard.js');
      const dashboardFile = generatePerformanceDashboard(this.results.tests);
      console.log(`   ✅ Dashboard generated: ${dashboardFile}`);
      this.results.dashboardFile = dashboardFile;
    } catch (error) {
      console.log(`   ⚠️  Dashboard generation failed: ${error.message}`);
    }
  }

  async generateSummaryReport() {
    console.log('\n📋 Generating Summary Report...');
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportFile = `test-summary-${timestamp}.md`;
    
    let report = `# Test Suite Summary Report
Generated: ${new Date().toLocaleString()}

## Environment
- Node.js Version: ${this.results.environment.nodeVersion}
- Test Timestamp: ${this.results.timestamp}

## Test Results Summary

`;

    // Add test results
    this.results.tests.forEach(test => {
      report += `### ${test.name.toUpperCase()} Test
Status: ${test.status === 'success' ? '✅ PASSED' : '❌ FAILED'}

`;
      if (test.status === 'failed' && test.error) {
        report += `Error Details:
\`\`\`
${test.error}
\`\`\`

`;
      }
    });

    // Add recommendations
    report += `## Recommendations

### Performance Optimization
1. Use direct stored procedures for maximum performance
2. Implement connection pooling for better resource management
3. Add result caching for frequently accessed data
4. Monitor query execution times in production

### Testing Best Practices
1. Run tests regularly to catch performance regressions
2. Test with production-like data volumes
3. Monitor memory usage during load testing
4. Set up automated performance alerts

### Next Steps
1. Review the generated performance dashboard
2. Implement the fastest approach for your use case
3. Set up continuous performance monitoring
4. Consider load testing with realistic data volumes

## Files Generated
- Performance Dashboard: ${this.results.dashboardFile || 'Not generated'}
- Test Summary: ${reportFile}

---
*Generated by Complete Test Suite Runner*
`;

    fs.writeFileSync(reportFile, report);
    console.log(`   ✅ Summary report generated: ${reportFile}`);
    
    // Save results as JSON for future analysis
    const resultsFile = `test-results-${timestamp}.json`;
    fs.writeFileSync(resultsFile, JSON.stringify(this.results, null, 2));
    console.log(`   ✅ Results data saved: ${resultsFile}`);
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help')) {
    console.log('Complete SP_FETCH_ITEMS Test Suite Runner');
    console.log('');
    console.log('Usage:');
    console.log('  node run-complete-test-suite.js           # Run complete test suite');
    console.log('  node run-complete-test-suite.js --help    # Show this help');
    console.log('');
    console.log('This script will:');
    console.log('1. Check environment and dependencies');
    console.log('2. Start the NestJS application if needed');
    console.log('3. Run sp_fetch_items performance tests');
    console.log('4. Run stored procedure comparison tests');
    console.log('5. Generate performance dashboard');
    console.log('6. Create summary report');
    return;
  }

  const runner = new TestSuiteRunner();
  await runner.runCompleteTestSuite();
}

if (require.main === module) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { TestSuiteRunner };
