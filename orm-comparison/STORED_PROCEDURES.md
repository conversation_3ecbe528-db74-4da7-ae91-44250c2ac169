# Stored Procedures Implementation and Benchmarking

This document provides a comprehensive overview of the stored procedure implementations across different ORMs and direct database access, along with performance benchmarking capabilities.

## Overview

The implementation includes:
1. **Direct Database Access** - Raw MySQL connection with stored procedure calls
2. **TypeORM Integration** - Stored procedure calls through TypeORM DataSource
3. **MikroORM Integration** - Stored procedure calls through MikroORM EntityManager
4. **Prisma Integration** - Stored procedure calls through Prisma's raw query interface
5. **Comprehensive Benchmarking** - Performance comparison across all approaches

## Stored Procedures Implemented

### Basic CRUD Operations
- `create_user(name, email, status)` - Creates a new user
- `get_users()` - Retrieves all users
- `get_user_by_id(id)` - Retrieves a specific user
- `update_user(id, name, email, status)` - Updates user information
- `delete_user(id)` - Deletes a user

### Complex Operations
- `benchmark_operations()` - Performs bulk operations for performance testing
- `sp_fetch_items(...)` - Complex work items fetching with filtering and grouping

## API Endpoints

### Direct Stored Procedure Access
```
POST   /stored-procedures/users           - Create user via stored procedure
GET    /stored-procedures/users           - Get all users via stored procedure
GET    /stored-procedures/users/:id       - Get user by ID via stored procedure
PUT    /stored-procedures/users/:id       - Update user via stored procedure
DELETE /stored-procedures/users/:id       - Delete user via stored procedure
POST   /stored-procedures/benchmark       - Run benchmark stored procedure
POST   /stored-procedures/fetch-items     - Complex items fetching
```

### ORM with Stored Procedures
Each ORM module includes stored procedure endpoints under `/sp/` path:

**TypeORM:**
```
POST   /typeorm/users/sp/users           - Create user via TypeORM + SP
GET    /typeorm/users/sp/users           - Get users via TypeORM + SP
POST   /typeorm/users/sp/benchmark       - Benchmark via TypeORM + SP
```

**MikroORM:**
```
POST   /mikroorm/users/sp/users          - Create user via MikroORM + SP
GET    /mikroorm/users/sp/users          - Get users via MikroORM + SP
POST   /mikroorm/users/sp/benchmark      - Benchmark via MikroORM + SP
```

**Prisma:**
```
POST   /prisma/users/sp/users            - Create user via Prisma + SP
GET    /prisma/users/sp/users            - Get users via Prisma + SP
POST   /prisma/users/sp/benchmark        - Benchmark via Prisma + SP
```

### Comprehensive Benchmarking
```
POST   /benchmark/users/create           - Compare all approaches for user creation
GET    /benchmark/users                  - Compare all approaches for user retrieval
POST   /benchmark/benchmark-operations   - Compare bulk operations
POST   /benchmark/fetch-items            - Compare complex item fetching
```

## Performance Comparison

The benchmark endpoints provide detailed performance analysis:

```json
{
  "operation": "create_user",
  "results": {
    "typeorm_orm": { "duration": 15.2, "method": "typeorm_orm" },
    "typeorm_sp": { "duration": 8.7, "method": "typeorm_stored_procedure" },
    "mikroorm_orm": { "duration": 12.1, "method": "mikroorm_orm" },
    "mikroorm_sp": { "duration": 9.3, "method": "mikroorm_stored_procedure" },
    "prisma_orm": { "duration": 10.5, "method": "prisma_orm" },
    "prisma_sp": { "duration": 7.9, "method": "prisma_stored_procedure" },
    "direct_sp": { "duration": 6.2, "method": "stored_procedure" }
  },
  "summary": {
    "fastest": "direct_sp",
    "slowest": "typeorm_orm",
    "performance_comparison": {
      "direct_sp": 1.0,
      "prisma_sp": 1.27,
      "typeorm_sp": 1.40,
      "mikroorm_sp": 1.50,
      "prisma_orm": 1.69,
      "mikroorm_orm": 1.95,
      "typeorm_orm": 2.45
    }
  }
}
```

## Implementation Details

### Direct Database Access
- Uses `mysql2` driver for direct connection
- Maintains connection pool per tenant
- Raw stored procedure calls with parameter binding
- Fastest performance but requires manual connection management

### TypeORM Integration
- Uses DataSource.query() for stored procedure calls
- Leverages existing TypeORM connection management
- Good balance between performance and ORM features
- Supports both ORM operations and stored procedures

### MikroORM Integration
- Uses EntityManager.getConnection().execute() for stored procedures
- Integrates with MikroORM's connection handling
- Maintains ORM benefits while allowing stored procedure access
- Good performance with familiar ORM interface

### Prisma Integration
- Uses $queryRawUnsafe() for stored procedure calls
- Works within Prisma's connection management
- Type-safe when possible, raw when needed
- Good integration with existing Prisma workflows

## Usage Examples

### Creating a User (All Methods)
```bash
# Direct stored procedure
curl -X POST http://localhost:3000/stored-procedures/users \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: devapi" \
  -d '{"name": "John Doe", "email": "<EMAIL>", "status": true}'

# TypeORM with stored procedure
curl -X POST http://localhost:3000/typeorm/users/sp/users \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: devapi" \
  -d '{"name": "John Doe", "email": "<EMAIL>", "status": true}'

# Comprehensive benchmark
curl -X POST http://localhost:3000/benchmark/users/create \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: devapi" \
  -d '{"name": "John Doe", "email": "<EMAIL>", "status": true}'
```

### Complex Item Fetching
```bash
curl -X POST http://localhost:3000/benchmark/fetch-items \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: devapi" \
  -d '{
    "jobIds": "1,2,3",
    "groupHow": 0,
    "includeArchived": false,
    "appType": "fab"
  }'
```

## Performance Insights

Based on typical benchmarks:

1. **Direct Stored Procedures** - Fastest (baseline)
2. **Prisma + Stored Procedures** - ~20-30% slower than direct
3. **TypeORM + Stored Procedures** - ~30-40% slower than direct
4. **MikroORM + Stored Procedures** - ~40-50% slower than direct
5. **ORM Operations** - ~60-150% slower than direct stored procedures

## Best Practices

1. **Use stored procedures for:**
   - Complex business logic
   - Bulk operations
   - Performance-critical operations
   - Data-intensive processing

2. **Choose implementation based on:**
   - **Direct SP**: Maximum performance, minimal overhead
   - **Prisma SP**: Good balance, excellent TypeScript integration
   - **TypeORM SP**: Familiar interface, good ecosystem
   - **MikroORM SP**: Modern approach, good performance

3. **Consider hybrid approach:**
   - Use ORM for simple CRUD operations
   - Use stored procedures for complex/performance-critical operations
   - Leverage benchmarking to identify bottlenecks

## Database Setup

Ensure the stored procedures are created in your database:

```sql
-- Run the stored procedures from prisma/stored-procedures.sql
-- and sp_fetch_items.sql in your database
```

## Monitoring and Optimization

Each implementation includes:
- Execution time measurement
- Performance comparison metrics
- Error handling and logging
- Connection management optimization

Use the benchmark endpoints to continuously monitor and optimize performance across different approaches.
