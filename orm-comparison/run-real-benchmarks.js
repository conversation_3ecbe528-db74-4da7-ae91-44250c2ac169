#!/usr/bin/env node

/**
 * Complete Real Benchmark Test Runner
 * This script sets up the database and runs actual performance tests
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class RealBenchmarkRunner {
  constructor() {
    this.results = null;
    this.startTime = new Date();
  }

  async checkPrerequisites() {
    console.log('🔍 Checking Prerequisites...');
    
    // Check if mysql2 is installed
    try {
      require('mysql2/promise');
      console.log('✅ mysql2 package found');
    } catch (error) {
      throw new Error('mysql2 package not found. Run: npm install mysql2');
    }

    // Check if @prisma/client is installed
    try {
      require('@prisma/client');
      console.log('✅ @prisma/client package found');
    } catch (error) {
      throw new Error('@prisma/client package not found. Run: npm install @prisma/client');
    }

    // Check environment variables
    const requiredEnvVars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME'];
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      console.log('⚠️  Missing environment variables:', missingVars.join(', '));
      console.log('   Using default values for missing variables');
    }

    console.log('✅ Prerequisites check complete\n');
  }

  async setupDatabase() {
    console.log('🔧 Setting up test database...');
    
    return new Promise((resolve, reject) => {
      const setupProcess = spawn('node', ['setup-test-database.js'], {
        stdio: 'pipe',
        env: process.env
      });

      let output = '';
      let errorOutput = '';

      setupProcess.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        process.stdout.write(text);
      });

      setupProcess.stderr.on('data', (data) => {
        const text = data.toString();
        errorOutput += text;
        process.stderr.write(text);
      });

      setupProcess.on('close', (code) => {
        if (code === 0) {
          console.log('✅ Database setup completed\n');
          resolve();
        } else {
          reject(new Error(`Database setup failed with code ${code}\n${errorOutput}`));
        }
      });
    });
  }

  async runRealBenchmarks() {
    console.log('🚀 Running real benchmark tests...');
    
    return new Promise((resolve, reject) => {
      const benchmarkProcess = spawn('node', ['real-benchmark-test.js'], {
        stdio: 'pipe',
        env: process.env
      });

      let output = '';
      let errorOutput = '';

      benchmarkProcess.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        process.stdout.write(text);
      });

      benchmarkProcess.stderr.on('data', (data) => {
        const text = data.toString();
        errorOutput += text;
        process.stderr.write(text);
      });

      benchmarkProcess.on('close', (code) => {
        if (code === 0) {
          console.log('✅ Real benchmark tests completed\n');
          
          // Try to find the results file
          const files = fs.readdirSync('.')
            .filter(f => f.startsWith('real-benchmark-results-') && f.endsWith('.json'))
            .sort()
            .reverse();
          
          if (files.length > 0) {
            try {
              this.results = JSON.parse(fs.readFileSync(files[0], 'utf8'));
              console.log(`📄 Results loaded from: ${files[0]}\n`);
            } catch (error) {
              console.log('⚠️  Could not load results file\n');
            }
          }
          
          resolve();
        } else {
          reject(new Error(`Benchmark tests failed with code ${code}\n${errorOutput}`));
        }
      });
    });
  }

  generateSummaryReport() {
    console.log('📊 REAL BENCHMARK SUMMARY REPORT');
    console.log('=' .repeat(70));
    
    if (!this.results) {
      console.log('❌ No results data available');
      return;
    }

    const { overallStats, scenarios, database } = this.results;
    
    console.log(`🕒 Test Completed: ${new Date(this.results.endTime).toLocaleString()}`);
    console.log(`⏱️  Total Duration: ${this.results.duration.toFixed(1)} seconds`);
    console.log(`🗄️  Database: ${database.host}:${database.port}/${database.database}`);
    console.log('');

    // Overall performance ranking
    if (overallStats) {
      console.log('🏆 OVERALL PERFORMANCE RANKING:');
      Object.entries(overallStats)
        .sort((a, b) => a[1].average - b[1].average)
        .forEach(([method, stats], index) => {
          const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '📍';
          console.log(`${medal} ${(index + 1)}. ${method.padEnd(15)}: ${stats.average.toFixed(2)}ms average`);
        });
      console.log('');
    }

    // Scenario breakdown
    console.log('📋 SCENARIO BREAKDOWN:');
    scenarios.forEach((scenario, index) => {
      console.log(`${index + 1}. ${scenario.scenario}`);
      
      if (scenario.summary && scenario.summary.fastest) {
        console.log(`   🏆 Fastest: ${scenario.summary.fastest} (${scenario.summary.fastestTime.toFixed(2)}ms)`);
        console.log(`   🐌 Slowest: ${scenario.summary.slowest} (${scenario.summary.slowestTime.toFixed(2)}ms)`);
        
        console.log('   📈 Performance Ratios:');
        Object.entries(scenario.summary.performanceRatios).forEach(([method, ratio]) => {
          console.log(`      ${method.padEnd(15)}: ${ratio.toFixed(2)}x`);
        });
      } else {
        console.log('   ❌ No successful results for this scenario');
      }
      console.log('');
    });

    // Performance insights
    this.generatePerformanceInsights();
  }

  generatePerformanceInsights() {
    if (!this.results || !this.results.overallStats) return;

    console.log('💡 PERFORMANCE INSIGHTS:');
    
    const stats = this.results.overallStats;
    const methods = Object.keys(stats).sort((a, b) => stats[a].average - stats[b].average);
    
    if (methods.length >= 2) {
      const fastest = methods[0];
      const slowest = methods[methods.length - 1];
      const speedup = (stats[slowest].average / stats[fastest].average).toFixed(1);
      
      console.log(`1. ${fastest} is ${speedup}x faster than ${slowest}`);
    }

    // Calculate overhead percentages
    if (stats.direct_sp) {
      const baseline = stats.direct_sp.average;
      Object.entries(stats).forEach(([method, methodStats]) => {
        if (method !== 'direct_sp') {
          const overhead = ((methodStats.average - baseline) / baseline * 100).toFixed(1);
          console.log(`2. ${method} adds ${overhead}% overhead vs direct stored procedures`);
        }
      });
    }

    // Performance recommendations
    console.log('');
    console.log('🎯 RECOMMENDATIONS:');
    
    if (stats.direct_sp) {
      console.log('✅ Use direct stored procedures for maximum performance');
    }
    
    if (stats.prisma_sp && stats.direct_sp) {
      const prismaOverhead = ((stats.prisma_sp.average - stats.direct_sp.average) / stats.direct_sp.average * 100);
      if (prismaOverhead < 30) {
        console.log('✅ Prisma offers good performance with excellent developer experience');
      }
    }
    
    if (stats.typeorm_sp && stats.direct_sp) {
      const typeormOverhead = ((stats.typeorm_sp.average - stats.direct_sp.average) / stats.direct_sp.average * 100);
      if (typeormOverhead < 50) {
        console.log('✅ TypeORM provides reasonable performance for familiar patterns');
      }
    }

    console.log('✅ Consider caching for frequently accessed data');
    console.log('✅ Monitor performance in production with real data volumes');
    console.log('');
  }

  async generateMarkdownReport() {
    if (!this.results) return;

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `REAL_BENCHMARK_REPORT_${timestamp}.md`;
    
    let markdown = `# Real SP_FETCH_ITEMS Benchmark Report

Generated: ${new Date().toLocaleString()}
Database: ${this.results.database.host}:${this.results.database.port}/${this.results.database.database}
Test Duration: ${this.results.duration.toFixed(1)} seconds

## Executive Summary

This report contains **REAL performance data** from actual database execution of the sp_fetch_items stored procedure across different ORM implementations.

## Overall Performance Results

| Rank | Method | Average Time | Performance vs Fastest |
|------|--------|--------------|------------------------|
`;

    if (this.results.overallStats) {
      Object.entries(this.results.overallStats)
        .sort((a, b) => a[1].average - b[1].average)
        .forEach(([method, stats], index) => {
          const fastest = Object.values(this.results.overallStats).sort((a, b) => a.average - b.average)[0];
          const ratio = (stats.average / fastest.average).toFixed(2);
          const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '📍';
          markdown += `| ${medal} ${index + 1} | ${method} | ${stats.average.toFixed(2)}ms | ${ratio}x |\n`;
        });
    }

    markdown += `\n## Detailed Scenario Results\n\n`;

    this.results.scenarios.forEach((scenario, index) => {
      markdown += `### ${index + 1}. ${scenario.scenario}\n\n`;
      markdown += `**Description**: ${scenario.description}\n\n`;
      
      if (scenario.results) {
        markdown += `| Method | Average | Min | Max | Success Rate |\n`;
        markdown += `|--------|---------|-----|-----|-------------|\n`;
        
        Object.entries(scenario.results).forEach(([method, result]) => {
          if (result.average !== null) {
            markdown += `| ${method} | ${result.average.toFixed(2)}ms | ${result.min.toFixed(2)}ms | ${result.max.toFixed(2)}ms | ${result.successRate.toFixed(1)}% |\n`;
          } else {
            markdown += `| ${method} | Failed | - | - | 0% |\n`;
          }
        });
        
        markdown += `\n`;
      }
    });

    markdown += `\n## Key Findings\n\n`;
    markdown += `- **Real execution times** measured from actual database calls\n`;
    markdown += `- **Multiple iterations** per test for statistical accuracy\n`;
    markdown += `- **Actual database overhead** for each ORM implementation\n`;
    markdown += `- **Production-ready** performance characteristics\n\n`;

    markdown += `## Raw Data\n\n`;
    markdown += `Complete test results are available in the JSON file: \`real-benchmark-results-${this.results.timestamp.replace(/[:.]/g, '-')}.json\`\n`;

    fs.writeFileSync(filename, markdown);
    console.log(`📄 Markdown report generated: ${filename}`);
  }

  async run() {
    console.log('🚀 Real Benchmark Test Runner');
    console.log('=' .repeat(50));
    console.log('This will run ACTUAL performance tests with REAL database execution\n');

    try {
      await this.checkPrerequisites();
      await this.setupDatabase();
      await this.runRealBenchmarks();
      this.generateSummaryReport();
      await this.generateMarkdownReport();
      
      console.log('🎉 Real benchmark testing completed successfully!');
      console.log('');
      console.log('📁 Generated Files:');
      console.log('   - JSON results file with raw data');
      console.log('   - Markdown report with analysis');
      console.log('');
      console.log('🔍 Next Steps:');
      console.log('   1. Review the performance results');
      console.log('   2. Compare with your performance requirements');
      console.log('   3. Choose the best ORM for your use case');
      console.log('   4. Implement performance monitoring in production');
      
    } catch (error) {
      console.error('❌ Real benchmark testing failed:', error.message);
      console.log('');
      console.log('💡 Troubleshooting:');
      console.log('1. Check database connection settings');
      console.log('2. Ensure database user has proper permissions');
      console.log('3. Verify sp_fetch_items stored procedure exists');
      console.log('4. Check that test data was created successfully');
      console.log('');
      console.log('🔧 Environment Variables:');
      console.log('   DB_HOST=your_host DB_USER=your_user DB_PASSWORD=your_pass DB_NAME=your_db node run-real-benchmarks.js');
      
      process.exit(1);
    }
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help')) {
    console.log('Real Benchmark Test Runner');
    console.log('');
    console.log('This script runs ACTUAL performance tests with REAL database execution');
    console.log('');
    console.log('Usage:');
    console.log('  node run-real-benchmarks.js           # Run complete real benchmark suite');
    console.log('  node run-real-benchmarks.js --help    # Show this help');
    console.log('');
    console.log('Environment Variables (required):');
    console.log('  DB_HOST=localhost        # Database host');
    console.log('  DB_PORT=3306            # Database port');
    console.log('  DB_USER=root            # Database user');
    console.log('  DB_PASSWORD=password    # Database password');
    console.log('  DB_NAME=test_db         # Database name');
    console.log('');
    console.log('Example:');
    console.log('  DB_HOST=localhost DB_USER=root DB_PASSWORD=mypass DB_NAME=testdb node run-real-benchmarks.js');
    console.log('');
    console.log('What this script does:');
    console.log('  1. Checks prerequisites (packages, environment)');
    console.log('  2. Sets up test database with sample data');
    console.log('  3. Runs actual sp_fetch_items performance tests');
    console.log('  4. Generates detailed performance reports');
    console.log('  5. Provides actionable recommendations');
    return;
  }

  const runner = new RealBenchmarkRunner();
  await runner.run();
}

if (require.main === module) {
  main();
}

module.exports = { RealBenchmarkRunner };
