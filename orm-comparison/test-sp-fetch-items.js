#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
const TENANT_ID = 'devapi';

const headers = {
  'Content-Type': 'application/json',
  'X-Tenant-Id': TENANT_ID,
};

// Test cases for sp_fetch_items
const testCases = [
  {
    name: 'Basic Ungrouped Fetch',
    description: 'Simple fetch without grouping or filtering',
    params: {
      groupHow: 0,
      includeArchived: false,
      appType: 'fab'
    }
  },
  {
    name: 'Filtered by Job IDs',
    description: 'Fetch items filtered by specific job IDs',
    params: {
      jobIds: '1,2,3',
      groupHow: 0,
      includeArchived: false,
      appType: 'fab'
    }
  },
  {
    name: 'Grouped by Columns',
    description: 'Group items by specific column IDs',
    params: {
      workItemColumnIds: '1,2,3',
      groupHow: 1,
      includeArchived: false,
      appType: 'fab'
    }
  },
  {
    name: 'Package and Spool Filtering',
    description: 'Filter by packages and spools',
    params: {
      packageIds: '10,20,30',
      drawingIds: '100,200,300',
      groupHow: 0,
      includeArchived: false,
      appType: 'fab'
    }
  },
  {
    name: 'Assignment-based Filtering',
    description: 'Filter by assignment status and current user',
    params: {
      isAssigned: true,
      currentUser: 123,
      groupHow: 0,
      includeArchived: false,
      appType: 'fab'
    }
  },
  {
    name: 'BOM Items Only',
    description: 'Fetch only BOM (Bill of Materials) items',
    params: {
      bom: true,
      groupHow: 0,
      includeArchived: false,
      appType: 'fab'
    }
  },
  {
    name: 'Complex Filtering with Grouping',
    description: 'Complex scenario with multiple filters and grouping',
    params: {
      jobIds: '1,2',
      packageIds: '10,20',
      stageIds: '5,6',
      isAssigned: true,
      currentUser: 123,
      bom: true,
      groupHow: 2,
      includeArchived: false,
      appType: 'fab'
    }
  },
  {
    name: 'Include Archived Items',
    description: 'Fetch including archived work items',
    params: {
      jobIds: '1,2,3',
      groupHow: 0,
      includeArchived: true,
      appType: 'fab'
    }
  },
  {
    name: 'Advanced Grouping by Material',
    description: 'Group by material properties (groupHow = 3)',
    params: {
      workItemColumnIds: '1,2,3,4',
      groupHow: 3,
      includeArchived: false,
      appType: 'fab'
    }
  },
  {
    name: 'Stage-based Filtering',
    description: 'Filter by specific stage IDs',
    params: {
      stageIds: '1,2,3,4,5',
      groupHow: 0,
      includeArchived: false,
      appType: 'fab'
    }
  }
];

async function testSpFetchItems() {
  console.log('🔍 Testing sp_fetch_items Stored Procedure Performance');
  console.log('=' .repeat(60));
  console.log('');

  let totalTests = 0;
  let successfulTests = 0;
  const performanceResults = [];

  for (const testCase of testCases) {
    totalTests++;
    console.log(`📊 Test ${totalTests}: ${testCase.name}`);
    console.log(`   Description: ${testCase.description}`);
    console.log(`   Parameters:`, JSON.stringify(testCase.params, null, 2).replace(/\n/g, '\n   '));
    
    try {
      // Test comprehensive benchmark
      const benchmarkResponse = await axios.post(
        `${BASE_URL}/benchmark/fetch-items`,
        testCase.params,
        { headers }
      );

      successfulTests++;
      console.log('   ✅ Performance Results:');
      
      const results = benchmarkResponse.data.results;
      const validResults = Object.entries(results).filter(([method, result]) => result.duration > 0);
      
      validResults.forEach(([method, result]) => {
        console.log(`      ${method.padEnd(20)}: ${result.duration.toFixed(2)}ms`);
      });
      
      console.log(`   🏆 Fastest Method: ${benchmarkResponse.data.summary.fastest}`);
      console.log(`   🐌 Slowest Method: ${benchmarkResponse.data.summary.slowest}`);
      
      // Store results for summary
      performanceResults.push({
        testName: testCase.name,
        fastest: benchmarkResponse.data.summary.fastest,
        results: validResults.map(([method, result]) => ({
          method,
          duration: result.duration
        }))
      });

      // Test individual implementations
      console.log('   🔧 Testing Individual Implementations:');
      
      // Direct stored procedure
      try {
        const directResponse = await axios.post(
          `${BASE_URL}/stored-procedures/fetch-items`,
          testCase.params,
          { headers }
        );
        console.log(`      Direct SP: ${directResponse.data.duration.toFixed(2)}ms`);
      } catch (error) {
        console.log(`      Direct SP: ❌ ${error.response?.status || 'Error'}`);
      }

      console.log('');
      
    } catch (error) {
      console.log(`   ❌ Test Failed: ${error.response?.status || error.message}`);
      if (error.response?.data) {
        console.log(`      Error Details:`, error.response.data);
      }
      console.log('');
    }
  }

  // Summary
  console.log('📈 PERFORMANCE SUMMARY');
  console.log('=' .repeat(60));
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Successful Tests: ${successfulTests}`);
  console.log(`Success Rate: ${((successfulTests / totalTests) * 100).toFixed(1)}%`);
  console.log('');

  if (performanceResults.length > 0) {
    console.log('🏆 FASTEST METHOD BY TEST:');
    performanceResults.forEach(result => {
      console.log(`   ${result.testName}: ${result.fastest}`);
    });
    console.log('');

    // Calculate overall performance statistics
    const methodStats = {};
    performanceResults.forEach(result => {
      result.results.forEach(({ method, duration }) => {
        if (!methodStats[method]) {
          methodStats[method] = { total: 0, count: 0, min: Infinity, max: 0 };
        }
        methodStats[method].total += duration;
        methodStats[method].count++;
        methodStats[method].min = Math.min(methodStats[method].min, duration);
        methodStats[method].max = Math.max(methodStats[method].max, duration);
      });
    });

    console.log('📊 OVERALL PERFORMANCE STATISTICS:');
    Object.entries(methodStats).forEach(([method, stats]) => {
      const avg = stats.total / stats.count;
      console.log(`   ${method.padEnd(25)}: Avg: ${avg.toFixed(2)}ms, Min: ${stats.min.toFixed(2)}ms, Max: ${stats.max.toFixed(2)}ms`);
    });
    console.log('');
  }

  console.log('💡 RECOMMENDATIONS:');
  console.log('   1. Use direct stored procedures for maximum performance');
  console.log('   2. Consider Prisma for good balance of performance and features');
  console.log('   3. Monitor complex queries with multiple filters');
  console.log('   4. Test with your actual data volume for realistic results');
  console.log('');

  console.log('🔧 TROUBLESHOOTING:');
  if (successfulTests === 0) {
    console.log('   ❌ No tests passed. Check:');
    console.log('      - NestJS application is running (npm run start:dev)');
    console.log('      - Database contains sp_fetch_items stored procedure');
    console.log('      - Tenant "devapi" is configured');
    console.log('      - Required tables exist (work_items, Jobs, Packages, etc.)');
  } else if (successfulTests < totalTests) {
    console.log('   ⚠️  Some tests failed. This might be due to:');
    console.log('      - Missing test data in database');
    console.log('      - Invalid parameter combinations');
    console.log('      - Database permission issues');
  } else {
    console.log('   ✅ All tests passed successfully!');
  }
}

// Additional utility functions
async function testSingleEndpoint(endpoint, params) {
  console.log(`\n🎯 Testing Single Endpoint: ${endpoint}`);
  try {
    const response = await axios.post(`${BASE_URL}${endpoint}`, params, { headers });
    console.log(`   ✅ Success: ${response.data.duration?.toFixed(2) || 'N/A'}ms`);
    console.log(`   📊 Data points returned: ${Array.isArray(response.data.data) ? response.data.data.length : 'N/A'}`);
    return response.data;
  } catch (error) {
    console.log(`   ❌ Failed: ${error.response?.status || error.message}`);
    return null;
  }
}

async function quickPerformanceTest() {
  console.log('⚡ Quick Performance Test');
  console.log('-'.repeat(30));
  
  const simpleParams = {
    groupHow: 0,
    includeArchived: false,
    appType: 'fab'
  };

  await testSingleEndpoint('/stored-procedures/fetch-items', simpleParams);
  await testSingleEndpoint('/benchmark/fetch-items', simpleParams);
}

// Command line interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--quick')) {
    quickPerformanceTest();
  } else if (args.includes('--help')) {
    console.log('Usage:');
    console.log('  node test-sp-fetch-items.js           # Run all tests');
    console.log('  node test-sp-fetch-items.js --quick   # Run quick test');
    console.log('  node test-sp-fetch-items.js --help    # Show this help');
  } else {
    testSpFetchItems();
  }
}

module.exports = { testSpFetchItems, testSingleEndpoint, quickPerformanceTest };
