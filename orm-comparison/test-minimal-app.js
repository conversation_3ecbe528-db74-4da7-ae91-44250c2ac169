#!/usr/bin/env node

/**
 * Minimal Test Application
 * Tests basic connectivity to your MariaDB database using your actual config
 */

const mysql = require('mysql2/promise');

// Import your actual database configuration
async function loadDatabaseConfig() {
  try {
    // Try to load the built configuration
    const { getDatabaseConfig } = require('./dist/config/database.config');
    return getDatabaseConfig('devapi');
  } catch (error) {
    console.log('⚠️  Could not load built config, using fallback');
    // Fallback configuration based on your database.config.ts
    return {
      host: 'localhost',
      port: 65107,
      user: 'root',
      password: 'admin',
      database: 'devapi'
    };
  }
}

async function testDatabaseConnection() {
  console.log('🔍 Testing Database Connection');
  console.log('=' .repeat(50));
  
  try {
    const config = await loadDatabaseConfig();
    console.log(`📋 Connecting to: ${config.host}:${config.port}/${config.database}`);
    
    const connection = await mysql.createConnection({
      host: config.host,
      port: config.port,
      user: config.user,
      password: config.password,
      database: config.database,
      connectTimeout: 10000
    });
    
    console.log('✅ Database connection successful!');
    
    // Test basic query
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log('✅ Basic query successful:', rows[0]);
    
    // Check if stored procedures exist
    const [procedures] = await connection.execute(
      "SELECT ROUTINE_NAME FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_SCHEMA = ? AND ROUTINE_TYPE = 'PROCEDURE'",
      [config.database]
    );
    
    console.log(`📊 Found ${procedures.length} stored procedures:`);
    procedures.forEach(proc => {
      console.log(`   - ${proc.ROUTINE_NAME}`);
    });
    
    // Test sp_fetch_items if it exists
    const spFetchItems = procedures.find(p => p.ROUTINE_NAME === 'sp_fetch_items');
    if (spFetchItems) {
      console.log('\n🧪 Testing sp_fetch_items...');
      try {
        const startTime = process.hrtime.bigint();
        const [results] = await connection.execute(
          'CALL sp_fetch_items(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
          [null, null, null, null, null, 0, 0, 0, null, 1, 0, 'fab']
        );
        const endTime = process.hrtime.bigint();
        const duration = Number(endTime - startTime) / 1000000;
        
        console.log(`✅ sp_fetch_items executed successfully!`);
        console.log(`   Duration: ${duration.toFixed(2)}ms`);
        console.log(`   Rows returned: ${Array.isArray(results) ? results.length : 0}`);
        
        if (Array.isArray(results) && results.length > 0) {
          console.log(`   Sample data: ${JSON.stringify(results[0], null, 2).substring(0, 200)}...`);
        }
      } catch (error) {
        console.log(`❌ sp_fetch_items test failed: ${error.message}`);
      }
    } else {
      console.log('⚠️  sp_fetch_items not found');
    }
    
    // Check data volume
    console.log('\n📊 Checking data volume...');
    try {
      const tables = ['work_items', 'jobs', 'packages', 'spools'];
      for (const table of tables) {
        try {
          const [count] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
          console.log(`   ${table}: ${count[0].count.toLocaleString()} rows`);
        } catch (error) {
          console.log(`   ${table}: Table not found or error`);
        }
      }
    } catch (error) {
      console.log(`   Error checking data volume: ${error.message}`);
    }
    
    await connection.end();
    
    console.log('\n🎉 Database connectivity test completed successfully!');
    console.log('\n💡 This confirms:');
    console.log('   ✅ Your MariaDB database is accessible');
    console.log('   ✅ Your database configuration is correct');
    console.log('   ✅ Stored procedures are available');
    console.log('   ✅ Real data is present for testing');
    console.log('\n🚀 Ready for API testing!');
    
    return true;
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.log('\n💡 Troubleshooting:');
    console.log('1. Check if MariaDB is running on port 65107');
    console.log('2. Verify database credentials');
    console.log('3. Ensure database "devapi" exists');
    console.log('4. Check firewall/network connectivity');
    
    return false;
  }
}

async function testPrismaConnection() {
  console.log('\n🔷 Testing Prisma Connection');
  console.log('=' .repeat(50));
  
  try {
    const { PrismaClient } = require('@prisma/client');
    const config = await loadDatabaseConfig();
    
    const connectionUrl = `mysql://${config.user}:${config.password}@${config.host}:${config.port}/${config.database}`;
    
    const prisma = new PrismaClient({
      datasources: {
        db: { url: connectionUrl }
      }
    });
    
    // Test raw query
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Prisma connection successful:', result);
    
    // Test stored procedure call
    try {
      const spResult = await prisma.$queryRawUnsafe(
        'CALL sp_fetch_items(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        null, null, null, null, null, 0, 0, 0, null, 1, 0, 'fab'
      );
      console.log('✅ Prisma stored procedure call successful');
      console.log(`   Rows returned: ${Array.isArray(spResult) ? spResult.length : 0}`);
    } catch (error) {
      console.log(`⚠️  Prisma stored procedure test failed: ${error.message}`);
    }
    
    await prisma.$disconnect();
    console.log('✅ Prisma test completed successfully!');
    
    return true;
    
  } catch (error) {
    console.error('❌ Prisma test failed:', error.message);
    return false;
  }
}

async function generateAPITestCommands() {
  console.log('\n🔧 API Test Commands');
  console.log('=' .repeat(50));
  
  const config = await loadDatabaseConfig();
  
  console.log('Once your NestJS application is running, test these endpoints:');
  console.log('');
  console.log('# Health check');
  console.log('curl http://localhost:3000/');
  console.log('');
  console.log('# Stored procedure endpoints');
  console.log('curl -H "X-Tenant-Id: devapi" http://localhost:3000/stored-procedures/users');
  console.log('');
  console.log('curl -X POST -H "Content-Type: application/json" -H "X-Tenant-Id: devapi" \\');
  console.log('  -d \'{"name": "Test User", "email": "<EMAIL>", "status": true}\' \\');
  console.log('  http://localhost:3000/stored-procedures/users');
  console.log('');
  console.log('# sp_fetch_items test');
  console.log('curl -X POST -H "Content-Type: application/json" -H "X-Tenant-Id: devapi" \\');
  console.log('  -d \'{"groupHow": 0, "includeArchived": false, "appType": "fab"}\' \\');
  console.log('  http://localhost:3000/stored-procedures/fetch-items');
  console.log('');
  console.log('# Prisma endpoints');
  console.log('curl -H "X-Tenant-Id: devapi" http://localhost:3000/prisma/users');
  console.log('');
  console.log('curl -X POST -H "Content-Type: application/json" -H "X-Tenant-Id: devapi" \\');
  console.log('  -d \'{"groupHow": 0, "includeArchived": false, "appType": "fab"}\' \\');
  console.log('  http://localhost:3000/prisma/users/sp/fetch-items');
}

async function main() {
  console.log('🚀 Minimal Database & API Test');
  console.log(`🏢 Testing tenant: devapi`);
  console.log(`🗄️  Database: MariaDB v10.6 on port 65107`);
  console.log('=' .repeat(70));
  
  const dbSuccess = await testDatabaseConnection();
  
  if (dbSuccess) {
    const prismaSuccess = await testPrismaConnection();
    await generateAPITestCommands();
    
    console.log('\n🎯 SUMMARY:');
    console.log(`   Database Connection: ${dbSuccess ? '✅ Working' : '❌ Failed'}`);
    console.log(`   Prisma Connection: ${prismaSuccess ? '✅ Working' : '❌ Failed'}`);
    
    if (dbSuccess && prismaSuccess) {
      console.log('\n🎉 All tests passed! Your database setup is ready for API testing.');
      console.log('\n📋 Next steps:');
      console.log('   1. Fix the NestJS application build issues');
      console.log('   2. Start the application: npm run start:dev');
      console.log('   3. Test the API endpoints with the curl commands above');
    }
  }
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  });
}

module.exports = { testDatabaseConnection, testPrismaConnection };
