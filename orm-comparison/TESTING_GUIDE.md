# Complete Testing Guide for SP_FETCH_ITEMS

This guide provides step-by-step instructions for testing the sp_fetch_items stored procedure implementation across all ORM approaches.

## Quick Start (5 minutes)

### 1. Prerequisites Check
```bash
# Ensure Node.js v20 is active
node --version  # Should show v20.x.x

# Ensure dependencies are installed
npm install
```

### 2. Start Application
```bash
# Start in development mode
npm run start:dev

# Wait for "Application is running on: http://localhost:3000"
```

### 3. Run Quick Test
```bash
# Quick performance test
node test-sp-fetch-items.js --quick

# Expected output:
# ⚡ Quick Performance Test
# 🎯 Testing Single Endpoint: /stored-procedures/fetch-items
#    ✅ Success: 65.23ms
```

## Comprehensive Testing (15 minutes)

### 1. Run Complete Test Suite
```bash
# Run all sp_fetch_items tests
node test-sp-fetch-items.js

# This will test:
# - Basic ungrouped fetch
# - Filtered by job IDs
# - Grouped by columns
# - Complex filtering scenarios
# - Performance comparison across all ORMs
```

### 2. Run Stored Procedure Comparison
```bash
# Test all stored procedure implementations
node test-stored-procedures.js

# This will test:
# - CRUD operations via stored procedures
# - Performance comparison across ORMs
# - Benchmark operations
```

### 3. Generate Performance Dashboard
```bash
# Create visual performance report
node generate-performance-dashboard.js

# Generates: performance-dashboard-[timestamp].md
```

## Complete Automated Testing (30 minutes)

### Run Everything Automatically
```bash
# Complete test suite with reporting
node run-complete-test-suite.js

# This will:
# 1. Check environment
# 2. Start application if needed
# 3. Run all tests
# 4. Generate dashboard
# 5. Create summary report
```

## Manual Testing Scenarios

### Scenario 1: Basic Performance Test
```bash
curl -X POST http://localhost:3000/benchmark/fetch-items \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: devapi" \
  -d '{
    "groupHow": 0,
    "includeArchived": false,
    "appType": "fab"
  }'
```

**Expected Response:**
```json
{
  "operation": "sp_fetch_items",
  "results": {
    "direct_sp": { "duration": 52.3, "method": "stored_procedure" },
    "typeorm_sp": { "duration": 68.7, "method": "typeorm_stored_procedure" },
    "mikroorm_sp": { "duration": 75.2, "method": "mikroorm_stored_procedure" },
    "prisma_sp": { "duration": 61.8, "method": "prisma_stored_procedure" }
  },
  "summary": {
    "fastest": "direct_sp",
    "slowest": "mikroorm_sp",
    "performance_comparison": {
      "direct_sp": 1.0,
      "prisma_sp": 1.18,
      "typeorm_sp": 1.31,
      "mikroorm_sp": 1.44
    }
  }
}
```

### Scenario 2: Complex Filtering Test
```bash
curl -X POST http://localhost:3000/benchmark/fetch-items \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: devapi" \
  -d '{
    "jobIds": "1,2,3",
    "packageIds": "10,20",
    "stageIds": "5,6",
    "isAssigned": true,
    "currentUser": 123,
    "bom": true,
    "groupHow": 2,
    "includeArchived": false,
    "appType": "fab"
  }'
```

### Scenario 3: Individual ORM Testing
```bash
# Test TypeORM implementation
curl -X POST http://localhost:3000/typeorm/users/sp/fetch-items \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: devapi" \
  -d '{"groupHow": 0, "appType": "fab"}'

# Test MikroORM implementation
curl -X POST http://localhost:3000/mikroorm/users/sp/fetch-items \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: devapi" \
  -d '{"groupHow": 0, "appType": "fab"}'

# Test Prisma implementation
curl -X POST http://localhost:3000/prisma/users/sp/fetch-items \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: devapi" \
  -d '{"groupHow": 0, "appType": "fab"}'
```

## Load Testing

### Setup Artillery (Optional)
```bash
# Install artillery for load testing
npm install -g artillery

# Create load test configuration
cat > artillery-sp-fetch.yml << 'EOF'
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 5
  defaults:
    headers:
      X-Tenant-Id: 'devapi'
      Content-Type: 'application/json'

scenarios:
  - name: 'sp_fetch_items load test'
    requests:
      - post:
          url: '/benchmark/fetch-items'
          json:
            groupHow: 0
            includeArchived: false
            appType: 'fab'
EOF

# Run load test
artillery run artillery-sp-fetch.yml
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Application Not Starting
```bash
# Check if port 3000 is in use
lsof -i :3000

# Kill existing process if needed
kill -9 $(lsof -t -i:3000)

# Start application
npm run start:dev
```

#### 2. Database Connection Issues
```bash
# Check database configuration
cat src/config/environment.config.ts

# Verify tenant configuration exists
# Ensure 'devapi' tenant is configured in your database config
```

#### 3. Missing Stored Procedures
```sql
-- Run these in your database
SOURCE prisma/stored-procedures.sql;
SOURCE sp_fetch_items.sql;

-- Verify procedures exist
SHOW PROCEDURE STATUS WHERE Name LIKE 'sp_%';
```

#### 4. Permission Issues
```sql
-- Grant execute permissions
GRANT EXECUTE ON PROCEDURE sp_fetch_items TO 'your_db_user'@'%';
GRANT EXECUTE ON PROCEDURE create_user TO 'your_db_user'@'%';
-- ... grant for other procedures
```

### Debug Mode
```bash
# Enable detailed logging
DEBUG=true npm run start:dev

# Run tests with verbose output
DEBUG=true node test-sp-fetch-items.js
```

## Performance Expectations

### Typical Results (milliseconds)
| Scenario | Direct SP | Prisma SP | TypeORM SP | MikroORM SP |
|----------|-----------|-----------|------------|-------------|
| Basic Fetch | 50-80 | 65-100 | 75-120 | 85-140 |
| With Filtering | 60-90 | 75-110 | 85-130 | 95-150 |
| With Grouping | 70-100 | 85-120 | 95-140 | 105-160 |
| Complex Query | 80-120 | 100-140 | 115-160 | 125-180 |

### Performance Factors
- **Database Size**: Larger datasets increase execution time
- **Network Latency**: Database connection speed affects results
- **System Load**: CPU and memory usage impact performance
- **Query Complexity**: More filters and joins slow execution

## Continuous Monitoring

### Setup Performance Alerts
```javascript
// Add to your monitoring setup
const performanceThresholds = {
  'sp_fetch_items_direct': 100,    // ms
  'sp_fetch_items_prisma': 130,    // ms
  'sp_fetch_items_typeorm': 150,   // ms
  'sp_fetch_items_mikroorm': 170   // ms
};

function checkPerformance(method, duration) {
  if (duration > performanceThresholds[method]) {
    console.warn(`Performance alert: ${method} took ${duration}ms`);
    // Send alert to monitoring service
  }
}
```

### Regular Testing Schedule
```bash
# Add to crontab for regular testing
# Run performance tests every hour
0 * * * * cd /path/to/orm-comparison && node test-sp-fetch-items.js --quick >> performance.log 2>&1

# Generate daily performance report
0 9 * * * cd /path/to/orm-comparison && node generate-performance-dashboard.js
```

## Next Steps

1. **Baseline Performance**: Run tests with your actual data
2. **Optimize Queries**: Use results to identify bottlenecks
3. **Monitor Production**: Implement performance tracking
4. **Scale Testing**: Test with production-like load
5. **Continuous Improvement**: Regular performance reviews

---

For detailed flow diagrams and architecture explanations, see [sp_fetch_items_readme.md](./sp_fetch_items_readme.md)
