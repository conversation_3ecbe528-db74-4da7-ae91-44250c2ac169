#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Generates a performance dashboard with Mermaid diagrams
 * based on test results from sp_fetch_items testing
 */

function generatePerformanceDashboard(testResults) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `performance-dashboard-${timestamp}.md`;

  let dashboard = `# SP_FETCH_ITEMS Performance Dashboard
Generated: ${new Date().toLocaleString()}

## Executive Summary

This dashboard provides a comprehensive analysis of sp_fetch_items stored procedure performance across different ORM implementations.

`;

  // Generate overall performance comparison
  if (testResults && testResults.length > 0) {
    dashboard += generateOverallPerformanceChart(testResults);
    dashboard += generateDetailedAnalysis(testResults);
    dashboard += generateRecommendations(testResults);
  } else {
    dashboard += generateSampleDashboard();
  }

  dashboard += generateTestingInstructions();

  fs.writeFileSync(filename, dashboard);
  console.log(`📊 Performance dashboard generated: ${filename}`);
  return filename;
}

function generateOverallPerformanceChart(testResults) {
  const avgPerformance = calculateAveragePerformance(testResults);
  
  return `## Overall Performance Comparison

\`\`\`mermaid
graph LR
    A[Direct MySQL2<br/>${avgPerformance.direct_sp?.toFixed(1) || 'N/A'}ms] --> B[Baseline]
    C[Prisma SP<br/>${avgPerformance.prisma_sp?.toFixed(1) || 'N/A'}ms] --> D[${calculateRatio(avgPerformance.prisma_sp, avgPerformance.direct_sp)}x]
    E[TypeORM SP<br/>${avgPerformance.typeorm_sp?.toFixed(1) || 'N/A'}ms] --> F[${calculateRatio(avgPerformance.typeorm_sp, avgPerformance.direct_sp)}x]
    G[MikroORM SP<br/>${avgPerformance.mikroorm_sp?.toFixed(1) || 'N/A'}ms] --> H[${calculateRatio(avgPerformance.mikroorm_sp, avgPerformance.direct_sp)}x]
    
    style A fill:#90EE90,stroke:#006400,stroke-width:3px
    style C fill:#FFE4B5,stroke:#FF8C00,stroke-width:2px
    style E fill:#FFB6C1,stroke:#DC143C,stroke-width:2px
    style G fill:#FFA07A,stroke:#FF4500,stroke-width:2px
\`\`\`

`;
}

function generateDetailedAnalysis(testResults) {
  return `## Detailed Performance Analysis

### Performance by Test Scenario

\`\`\`mermaid
graph TD
    A[Test Scenarios] --> B[Basic Fetch]
    A --> C[Filtered Fetch]
    A --> D[Grouped Fetch]
    A --> E[Complex Fetch]
    
    B --> B1[Direct: ${getScenarioPerformance(testResults, 'Basic', 'direct_sp')}ms]
    B --> B2[Prisma: ${getScenarioPerformance(testResults, 'Basic', 'prisma_sp')}ms]
    B --> B3[TypeORM: ${getScenarioPerformance(testResults, 'Basic', 'typeorm_sp')}ms]
    B --> B4[MikroORM: ${getScenarioPerformance(testResults, 'Basic', 'mikroorm_sp')}ms]
    
    C --> C1[Direct: ${getScenarioPerformance(testResults, 'Filtered', 'direct_sp')}ms]
    C --> C2[Prisma: ${getScenarioPerformance(testResults, 'Filtered', 'prisma_sp')}ms]
    C --> C3[TypeORM: ${getScenarioPerformance(testResults, 'Filtered', 'typeorm_sp')}ms]
    C --> C4[MikroORM: ${getScenarioPerformance(testResults, 'Filtered', 'mikroorm_sp')}ms]
    
    style B1 fill:#90EE90
    style C1 fill:#90EE90
    style B2 fill:#FFE4B5
    style C2 fill:#FFE4B5
    style B3 fill:#FFB6C1
    style C3 fill:#FFB6C1
    style B4 fill:#FFA07A
    style C4 fill:#FFA07A
\`\`\`

### Performance Trends

\`\`\`mermaid
graph LR
    A[Simple Query] --> B[+0ms baseline]
    C[With Filtering] --> D[+${calculateFilteringOverhead(testResults)}ms]
    E[With Grouping] --> F[+${calculateGroupingOverhead(testResults)}ms]
    G[Complex Query] --> H[+${calculateComplexityOverhead(testResults)}ms]
    
    style A fill:#90EE90
    style C fill:#FFFF99
    style E fill:#FFB6C1
    style G fill:#FFA07A
\`\`\`

`;
}

function generateRecommendations(testResults) {
  const fastest = findFastestMethod(testResults);
  const slowest = findSlowestMethod(testResults);
  
  return `## Recommendations

### Performance Ranking
1. 🥇 **${fastest.method}** - Average: ${fastest.avgTime?.toFixed(1) || 'N/A'}ms
2. 🥈 **Second Best** - Based on your test results
3. 🥉 **Third Best** - Based on your test results
4. 🐌 **${slowest.method}** - Average: ${slowest.avgTime?.toFixed(1) || 'N/A'}ms

### Use Case Recommendations

\`\`\`mermaid
flowchart TD
    A[Choose Implementation] --> B{Performance Critical?}
    B -->|Yes| C[Direct MySQL2]
    B -->|No| D{TypeScript Integration?}
    D -->|Important| E[Prisma]
    D -->|Not Critical| F{Existing Codebase?}
    F -->|TypeORM| G[TypeORM]
    F -->|MikroORM| H[MikroORM]
    F -->|New Project| I[Prisma or Direct]
    
    style C fill:#90EE90
    style E fill:#FFE4B5
    style G fill:#FFB6C1
    style H fill:#FFA07A
\`\`\`

### Optimization Strategies

1. **Database Level**
   - Add indexes on frequently filtered columns
   - Optimize stored procedure logic
   - Use query caching for repeated calls

2. **Application Level**
   - Implement connection pooling
   - Add result caching (Redis)
   - Use batch processing for multiple calls

3. **Monitoring**
   - Set up performance alerts
   - Track query execution times
   - Monitor database connection usage

`;
}

function generateSampleDashboard() {
  return `## Sample Performance Data

*Run the test suite to generate actual performance data*

\`\`\`bash
# Run comprehensive tests
node test-sp-fetch-items.js

# Run quick test
node test-sp-fetch-items.js --quick
\`\`\`

### Expected Performance Hierarchy

\`\`\`mermaid
graph LR
    A[Direct MySQL2<br/>~50-80ms] --> B[Fastest]
    C[Prisma SP<br/>~65-100ms] --> D[Good Balance]
    E[TypeORM SP<br/>~75-120ms] --> F[Moderate]
    G[MikroORM SP<br/>~85-140ms] --> H[Slower]
    
    style A fill:#90EE90,stroke:#006400,stroke-width:3px
    style C fill:#FFE4B5,stroke:#FF8C00,stroke-width:2px
    style E fill:#FFB6C1,stroke:#DC143C,stroke-width:2px
    style G fill:#FFA07A,stroke:#FF4500,stroke-width:2px
\`\`\`

`;
}

function generateTestingInstructions() {
  return `## Testing Instructions

### Quick Start
\`\`\`bash
# 1. Start the application
npm run start:dev

# 2. Run performance tests
node test-sp-fetch-items.js

# 3. Generate updated dashboard
node generate-performance-dashboard.js
\`\`\`

### Advanced Testing
\`\`\`bash
# Load testing
npm install -g artillery
artillery run artillery-config.yml

# Memory profiling
node --inspect test-sp-fetch-items.js
\`\`\`

### Continuous Monitoring
\`\`\`javascript
// Add to your application
const performanceMonitor = {
  track: (method, duration) => {
    console.log(\`\${method}: \${duration}ms\`);
    // Send to monitoring service
  }
};
\`\`\`

---
*Dashboard generated by sp_fetch_items performance testing suite*
`;
}

// Utility functions
function calculateAveragePerformance(testResults) {
  const methods = ['direct_sp', 'prisma_sp', 'typeorm_sp', 'mikroorm_sp'];
  const averages = {};
  
  methods.forEach(method => {
    const times = testResults
      .flatMap(test => test.results || [])
      .filter(result => result.method === method)
      .map(result => result.duration);
    
    if (times.length > 0) {
      averages[method] = times.reduce((a, b) => a + b, 0) / times.length;
    }
  });
  
  return averages;
}

function calculateRatio(value, baseline) {
  if (!value || !baseline) return 'N/A';
  return (value / baseline).toFixed(1);
}

function getScenarioPerformance(testResults, scenarioType, method) {
  const scenario = testResults.find(test => 
    test.testName && test.testName.includes(scenarioType)
  );
  
  if (scenario && scenario.results) {
    const result = scenario.results.find(r => r.method === method);
    return result ? result.duration.toFixed(1) : 'N/A';
  }
  
  return 'N/A';
}

function calculateFilteringOverhead(testResults) {
  // Calculate average overhead when filtering is applied
  return '15-25'; // Placeholder
}

function calculateGroupingOverhead(testResults) {
  // Calculate average overhead when grouping is applied
  return '30-50'; // Placeholder
}

function calculateComplexityOverhead(testResults) {
  // Calculate overhead for complex queries
  return '50-100'; // Placeholder
}

function findFastestMethod(testResults) {
  // Find the consistently fastest method
  return { method: 'Direct MySQL2', avgTime: 65 }; // Placeholder
}

function findSlowestMethod(testResults) {
  // Find the consistently slowest method
  return { method: 'MikroORM SP', avgTime: 120 }; // Placeholder
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--help')) {
    console.log('Usage:');
    console.log('  node generate-performance-dashboard.js           # Generate dashboard');
    console.log('  node generate-performance-dashboard.js --help    # Show help');
  } else {
    // Try to load test results if available
    let testResults = null;
    try {
      if (fs.existsSync('test-results.json')) {
        testResults = JSON.parse(fs.readFileSync('test-results.json', 'utf8'));
      }
    } catch (error) {
      console.log('No test results found, generating sample dashboard');
    }
    
    generatePerformanceDashboard(testResults);
  }
}

module.exports = { generatePerformanceDashboard };
