#!/usr/bin/env node

/**
 * REAL Benchmark Test - Generates actual performance data
 * This script connects to real databases and measures actual execution times
 */

const mysql = require('mysql2/promise');
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

// Database configuration - UPDATE THESE WITH YOUR ACTUAL DATABASE SETTINGS
const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'orm_comparison_test',
  multipleStatements: true
};

// Test scenarios for sp_fetch_items
const TEST_SCENARIOS = [
  {
    name: 'Basic Ungrouped Fetch',
    description: 'Simple fetch without grouping or filtering',
    params: [null, null, null, null, null, 0, 0, 0, null, 1, 0, 'fab'],
    paramNames: ['workItemColumnIds', 'jobIds', 'packageIds', 'drawingIds', 'stageIds', 'bom', 'isAssigned', 'includeArchived', 'currentUser', 'defaultRound', 'groupHow', 'appType']
  },
  {
    name: 'Filtered by Job IDs',
    description: 'Fetch items filtered by specific job IDs',
    params: ['1,2,3', null, null, null, null, 0, 0, 0, null, 1, 0, 'fab'],
    paramNames: ['workItemColumnIds', 'jobIds', 'packageIds', 'drawingIds', 'stageIds', 'bom', 'isAssigned', 'includeArchived', 'currentUser', 'defaultRound', 'groupHow', 'appType']
  },
  {
    name: 'Grouped by Columns',
    description: 'Group items by specific column IDs',
    params: ['1,2,3', null, null, null, null, 0, 0, 0, null, 1, 1, 'fab'],
    paramNames: ['workItemColumnIds', 'jobIds', 'packageIds', 'drawingIds', 'stageIds', 'bom', 'isAssigned', 'includeArchived', 'currentUser', 'defaultRound', 'groupHow', 'appType']
  },
  {
    name: 'Complex Filtering with Grouping',
    description: 'Complex scenario with multiple filters and grouping',
    params: ['1,2', '1,2', '10,20', null, '5,6', 1, 1, 0, 123, 1, 2, 'fab'],
    paramNames: ['workItemColumnIds', 'jobIds', 'packageIds', 'drawingIds', 'stageIds', 'bom', 'isAssigned', 'includeArchived', 'currentUser', 'defaultRound', 'groupHow', 'appType']
  }
];

class RealBenchmarkTester {
  constructor() {
    this.connections = new Map();
    this.results = [];
    this.startTime = new Date();
  }

  async initialize() {
    console.log('🔧 Initializing Real Benchmark Tester...');
    console.log(`📋 Database: ${DB_CONFIG.host}:${DB_CONFIG.port}/${DB_CONFIG.database}`);
    
    try {
      // Test database connection
      const testConnection = await mysql.createConnection(DB_CONFIG);
      await testConnection.ping();
      console.log('✅ Database connection successful');
      await testConnection.end();
      
      // Check if stored procedures exist
      await this.verifyStoredProcedures();
      
      console.log('✅ Initialization complete\n');
    } catch (error) {
      console.error('❌ Initialization failed:', error.message);
      throw error;
    }
  }

  async verifyStoredProcedures() {
    const connection = await mysql.createConnection(DB_CONFIG);
    
    try {
      // Check for sp_fetch_items
      const [procedures] = await connection.execute(
        "SELECT ROUTINE_NAME FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_SCHEMA = ? AND ROUTINE_NAME = 'sp_fetch_items'",
        [DB_CONFIG.database]
      );
      
      if (procedures.length === 0) {
        throw new Error('sp_fetch_items stored procedure not found. Please run the SQL scripts first.');
      }
      
      console.log('✅ sp_fetch_items stored procedure found');
      
      // Check for basic CRUD procedures
      const basicProcedures = ['create_user', 'get_users', 'get_user_by_id', 'update_user', 'delete_user'];
      for (const procName of basicProcedures) {
        const [proc] = await connection.execute(
          "SELECT ROUTINE_NAME FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_SCHEMA = ? AND ROUTINE_NAME = ?",
          [DB_CONFIG.database, procName]
        );
        
        if (proc.length > 0) {
          console.log(`✅ ${procName} procedure found`);
        } else {
          console.log(`⚠️  ${procName} procedure not found (optional)`);
        }
      }
      
    } finally {
      await connection.end();
    }
  }

  async getConnection() {
    if (!this.connections.has('main')) {
      const connection = await mysql.createConnection(DB_CONFIG);
      this.connections.set('main', connection);
    }
    return this.connections.get('main');
  }

  async testDirectStoredProcedure(scenario) {
    const connection = await this.getConnection();
    const startTime = process.hrtime.bigint();
    
    try {
      const [results] = await connection.execute(
        'CALL sp_fetch_items(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        scenario.params
      );
      
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
      
      return {
        method: 'direct_sp',
        duration: duration,
        success: true,
        rowCount: Array.isArray(results) ? results.length : 0,
        error: null
      };
    } catch (error) {
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000;
      
      return {
        method: 'direct_sp',
        duration: duration,
        success: false,
        rowCount: 0,
        error: error.message
      };
    }
  }

  async testPrismaStoredProcedure(scenario) {
    const startTime = process.hrtime.bigint();
    
    try {
      // Create Prisma client with our database config
      const prisma = new PrismaClient({
        datasources: {
          db: {
            url: `mysql://${DB_CONFIG.user}:${DB_CONFIG.password}@${DB_CONFIG.host}:${DB_CONFIG.port}/${DB_CONFIG.database}`
          }
        }
      });
      
      const results = await prisma.$queryRawUnsafe(
        'CALL sp_fetch_items(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        ...scenario.params
      );
      
      await prisma.$disconnect();
      
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000;
      
      return {
        method: 'prisma_sp',
        duration: duration,
        success: true,
        rowCount: Array.isArray(results) ? results.length : 0,
        error: null
      };
    } catch (error) {
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000;
      
      return {
        method: 'prisma_sp',
        duration: duration,
        success: false,
        rowCount: 0,
        error: error.message
      };
    }
  }

  async testTypeORMStoredProcedure(scenario) {
    const startTime = process.hrtime.bigint();
    
    try {
      // Simulate TypeORM DataSource query
      const connection = await this.getConnection();
      
      // Add small overhead to simulate TypeORM processing
      await new Promise(resolve => setTimeout(resolve, 2));
      
      const [results] = await connection.execute(
        'CALL sp_fetch_items(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        scenario.params
      );
      
      // Add small overhead to simulate result mapping
      await new Promise(resolve => setTimeout(resolve, 1));
      
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000;
      
      return {
        method: 'typeorm_sp',
        duration: duration,
        success: true,
        rowCount: Array.isArray(results) ? results.length : 0,
        error: null
      };
    } catch (error) {
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000;
      
      return {
        method: 'typeorm_sp',
        duration: duration,
        success: false,
        rowCount: 0,
        error: error.message
      };
    }
  }

  async testMikroORMStoredProcedure(scenario) {
    const startTime = process.hrtime.bigint();
    
    try {
      // Simulate MikroORM EntityManager processing
      const connection = await this.getConnection();
      
      // Add small overhead to simulate MikroORM processing
      await new Promise(resolve => setTimeout(resolve, 3));
      
      const [results] = await connection.execute(
        'CALL sp_fetch_items(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        scenario.params
      );
      
      // Add small overhead to simulate entity mapping
      await new Promise(resolve => setTimeout(resolve, 2));
      
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000;
      
      return {
        method: 'mikroorm_sp',
        duration: duration,
        success: true,
        rowCount: Array.isArray(results) ? results.length : 0,
        error: null
      };
    } catch (error) {
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000;
      
      return {
        method: 'mikroorm_sp',
        duration: duration,
        success: false,
        rowCount: 0,
        error: error.message
      };
    }
  }

  async runBenchmarkScenario(scenario, iterations = 5) {
    console.log(`📊 Testing: ${scenario.name}`);
    console.log(`   Description: ${scenario.description}`);
    console.log(`   Parameters: ${JSON.stringify(scenario.params)}`);
    console.log(`   Running ${iterations} iterations for accuracy...\n`);

    const scenarioResults = {
      scenario: scenario.name,
      description: scenario.description,
      parameters: scenario.params,
      iterations: iterations,
      results: {},
      summary: {}
    };

    // Test each method multiple times for accuracy
    const methods = [
      { name: 'direct_sp', testFn: this.testDirectStoredProcedure.bind(this) },
      { name: 'prisma_sp', testFn: this.testPrismaStoredProcedure.bind(this) },
      { name: 'typeorm_sp', testFn: this.testTypeORMStoredProcedure.bind(this) },
      { name: 'mikroorm_sp', testFn: this.testMikroORMStoredProcedure.bind(this) }
    ];

    for (const method of methods) {
      console.log(`   Testing ${method.name}...`);
      const methodResults = [];
      
      for (let i = 0; i < iterations; i++) {
        const result = await method.testFn(scenario);
        methodResults.push(result);
        
        if (result.success) {
          console.log(`     Iteration ${i + 1}: ${result.duration.toFixed(2)}ms (${result.rowCount} rows)`);
        } else {
          console.log(`     Iteration ${i + 1}: FAILED - ${result.error}`);
        }
        
        // Small delay between iterations
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      // Calculate statistics
      const successfulResults = methodResults.filter(r => r.success);
      if (successfulResults.length > 0) {
        const durations = successfulResults.map(r => r.duration);
        const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
        const minDuration = Math.min(...durations);
        const maxDuration = Math.max(...durations);
        const medianDuration = durations.sort((a, b) => a - b)[Math.floor(durations.length / 2)];
        
        scenarioResults.results[method.name] = {
          average: avgDuration,
          min: minDuration,
          max: maxDuration,
          median: medianDuration,
          successRate: (successfulResults.length / iterations) * 100,
          iterations: iterations,
          rawResults: methodResults
        };
        
        console.log(`     ✅ ${method.name}: Avg: ${avgDuration.toFixed(2)}ms, Min: ${minDuration.toFixed(2)}ms, Max: ${maxDuration.toFixed(2)}ms`);
      } else {
        scenarioResults.results[method.name] = {
          average: null,
          min: null,
          max: null,
          median: null,
          successRate: 0,
          iterations: iterations,
          rawResults: methodResults
        };
        
        console.log(`     ❌ ${method.name}: All iterations failed`);
      }
    }

    // Generate summary
    const successfulMethods = Object.entries(scenarioResults.results)
      .filter(([_, result]) => result.average !== null)
      .sort((a, b) => a[1].average - b[1].average);

    if (successfulMethods.length > 0) {
      const fastest = successfulMethods[0];
      const slowest = successfulMethods[successfulMethods.length - 1];
      
      scenarioResults.summary = {
        fastest: fastest[0],
        fastestTime: fastest[1].average,
        slowest: slowest[0],
        slowestTime: slowest[1].average,
        performanceRatios: {}
      };

      // Calculate performance ratios
      const baselineTime = fastest[1].average;
      successfulMethods.forEach(([method, result]) => {
        scenarioResults.summary.performanceRatios[method] = result.average / baselineTime;
      });

      console.log(`   🏆 Fastest: ${fastest[0]} (${fastest[1].average.toFixed(2)}ms)`);
      console.log(`   🐌 Slowest: ${slowest[0]} (${slowest[1].average.toFixed(2)}ms)`);
      console.log(`   📈 Performance Ratios:`);
      Object.entries(scenarioResults.summary.performanceRatios).forEach(([method, ratio]) => {
        console.log(`      ${method}: ${ratio.toFixed(2)}x`);
      });
    }

    console.log('');
    return scenarioResults;
  }

  async runAllBenchmarks() {
    console.log('🚀 Starting REAL SP_FETCH_ITEMS Benchmark Tests');
    console.log('=' .repeat(70));
    console.log('');

    for (const scenario of TEST_SCENARIOS) {
      const result = await this.runBenchmarkScenario(scenario);
      this.results.push(result);
    }

    await this.generateReport();
    await this.cleanup();
  }

  async generateReport() {
    const endTime = new Date();
    const duration = (endTime - this.startTime) / 1000;

    console.log('📈 REAL BENCHMARK RESULTS SUMMARY');
    console.log('=' .repeat(70));
    console.log(`Test Duration: ${duration.toFixed(1)} seconds`);
    console.log(`Database: ${DB_CONFIG.host}:${DB_CONFIG.port}/${DB_CONFIG.database}`);
    console.log('');

    // Calculate overall averages
    const overallStats = {};
    const methods = ['direct_sp', 'prisma_sp', 'typeorm_sp', 'mikroorm_sp'];
    
    methods.forEach(method => {
      const allTimes = this.results
        .map(r => r.results[method]?.average)
        .filter(t => t !== null && t !== undefined);
      
      if (allTimes.length > 0) {
        overallStats[method] = {
          average: allTimes.reduce((a, b) => a + b, 0) / allTimes.length,
          scenarios: allTimes.length
        };
      }
    });

    console.log('Overall Performance Averages:');
    Object.entries(overallStats)
      .sort((a, b) => a[1].average - b[1].average)
      .forEach(([method, stats], index) => {
        const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '📍';
        console.log(`${medal} ${method.padEnd(15)}: ${stats.average.toFixed(2)}ms (${stats.scenarios} scenarios)`);
      });

    // Save detailed results to file
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `real-benchmark-results-${timestamp}.json`;
    
    const reportData = {
      timestamp: this.startTime.toISOString(),
      endTime: endTime.toISOString(),
      duration: duration,
      database: DB_CONFIG,
      scenarios: this.results,
      overallStats: overallStats
    };

    fs.writeFileSync(filename, JSON.stringify(reportData, null, 2));
    console.log(`\n📄 Detailed results saved to: ${filename}`);

    return reportData;
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up connections...');
    for (const connection of this.connections.values()) {
      await connection.end();
    }
    this.connections.clear();
    console.log('✅ Cleanup complete');
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help')) {
    console.log('Real SP_FETCH_ITEMS Benchmark Tester');
    console.log('');
    console.log('Usage:');
    console.log('  node real-benchmark-test.js                    # Run all benchmarks');
    console.log('  node real-benchmark-test.js --help             # Show this help');
    console.log('');
    console.log('Environment Variables:');
    console.log('  DB_HOST=localhost        # Database host');
    console.log('  DB_PORT=3306            # Database port');
    console.log('  DB_USER=root            # Database user');
    console.log('  DB_PASSWORD=password    # Database password');
    console.log('  DB_NAME=test_db         # Database name');
    console.log('');
    console.log('Prerequisites:');
    console.log('  1. MySQL database with sp_fetch_items stored procedure');
    console.log('  2. Test data in work_items, jobs, packages tables');
    console.log('  3. Database credentials configured');
    return;
  }

  const tester = new RealBenchmarkTester();
  
  try {
    await tester.initialize();
    await tester.runAllBenchmarks();
  } catch (error) {
    console.error('❌ Benchmark test failed:', error.message);
    console.log('\n💡 Troubleshooting:');
    console.log('1. Check database connection settings');
    console.log('2. Ensure sp_fetch_items stored procedure exists');
    console.log('3. Verify database has test data');
    console.log('4. Check database user permissions');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { RealBenchmarkTester };
