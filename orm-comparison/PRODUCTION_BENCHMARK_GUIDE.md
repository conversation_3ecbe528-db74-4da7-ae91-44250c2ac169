# Production Benchmark Testing Guide

This guide explains how to run **REAL performance benchmarks** using your **actual production databases** with **real data**.

## 🎯 What This Does

This framework connects to your actual production databases (devapi, staging3_dev) and runs real performance tests:

- ✅ **Uses your actual database.config.ts** configurations
- ✅ **Connects to real production databases** with real data
- ✅ **Executes actual sp_fetch_items** stored procedure
- ✅ **Measures real execution times** on production data volumes
- ✅ **Tests realistic use cases** (dashboard queries, filtering, reporting)
- ✅ **Provides statistical analysis** with multiple iterations
- ✅ **Generates executive reports** for decision making

## 🚀 Quick Start (3 steps)

### 1. Verify Database Connectivity
```bash
# Check if your databases are accessible and ready
node verify-production-databases.js
```

**Expected Output:**
```
🚀 Production Database Verification
==================================================

🔍 Verifying tenant: devapi
   📋 Config: your-host:3306/your-database
   ✅ Database connection successful
   📊 Checking data...
      work_items: 45,234 rows
      jobs: 1,247 rows
      packages: 3,891 rows
      spools: 8,456 rows
   🔧 Checking stored procedures...
      sp_fetch_items: ✅ Found
   🧪 Testing sp_fetch_items...
      ✅ sp_fetch_items test successful: 67.23ms, 1,234 rows
   ✅ devapi verification complete
```

### 2. Run Production Benchmarks
```bash
# Run benchmarks on devapi database
node run-production-benchmarks.js devapi

# Or run on staging3_dev database
node run-production-benchmarks.js staging3_dev
```

### 3. Review Results
The script will generate:
- **JSON file**: Raw performance data
- **Markdown report**: Executive summary with recommendations

## 📊 Expected Real Results

### Sample Production Output
```
🚀 Starting PRODUCTION SP_FETCH_ITEMS Benchmark Tests
🏢 Tenant: devapi
🗄️  Database: your-host:3306/your-database
📊 Work Items: 45,234

📊 Testing: Basic Production Fetch
   Description: Simple fetch without grouping - typical dashboard query
   Use Case: Dashboard loading, basic work item lists
   Running 5 iterations on PRODUCTION data...

   Testing direct_sp on production data...
     Iteration 1: 67.23ms (1,234 rows)
     Iteration 2: 65.89ms (1,234 rows)
     Iteration 3: 68.45ms (1,234 rows)
     Iteration 4: 66.12ms (1,234 rows)
     Iteration 5: 67.78ms (1,234 rows)
     ✅ direct_sp: Avg: 67.09ms, Min: 65.89ms, Max: 68.45ms, StdDev: 1.02ms

   Testing prisma_sp on production data...
     Iteration 1: 89.34ms (1,234 rows)
     Iteration 2: 87.56ms (1,234 rows)
     Iteration 3: 90.12ms (1,234 rows)
     Iteration 4: 88.78ms (1,234 rows)
     Iteration 5: 89.67ms (1,234 rows)
     ✅ prisma_sp: Avg: 89.09ms, Min: 87.56ms, Max: 90.12ms, StdDev: 0.98ms

   🏆 Fastest: direct_sp (67.09ms)
   🐌 Slowest: mikroorm_sp (112.45ms)
   📊 Data Volume: 1,234 rows returned
   📈 Performance Ratios:
      direct_sp: 1.00x
      prisma_sp: 1.33x
      typeorm_sp: 1.52x
      mikroorm_sp: 1.68x
```

### Final Executive Summary
```
📊 EXECUTIVE SUMMARY - PRODUCTION BENCHMARK RESULTS
================================================================================
🏢 Tenant: devapi
🗄️  Database: your-host:3306/your-database
📊 Data Volume: 45,234 work items
⏱️  Test Completed: 12/10/2024, 3:45:23 PM
⏱️  Total Duration: 125.7 seconds

📈 PRODUCTION DATA CONTEXT:
   Work Items: 45,234
   Jobs: 1,247
   Packages: 3,891
   Spools: 8,456
   Archived Items: 2,156
   Assigned Items: 12,789

🏆 OVERALL PERFORMANCE RANKING (Production Data):
🥇 1. direct_sp      : 78.45ms average
🥈 2. prisma_sp      : 102.67ms average
🥉 3. typeorm_sp     : 125.33ms average
📍 4. mikroorm_sp    : 142.91ms average

💡 PRODUCTION PERFORMANCE INSIGHTS:
1. direct_sp is 1.8x faster than mikroorm_sp on production data
2. prisma_sp adds 30.9% overhead vs direct stored procedures
3. typeorm_sp adds 59.7% overhead vs direct stored procedures
4. mikroorm_sp adds 82.2% overhead vs direct stored procedures
5. Large dataset (45,234 items) - performance differences are significant

🎯 PRODUCTION RECOMMENDATIONS:
✅ Direct stored procedures provide best performance for production workloads
✅ Prisma offers good production performance - acceptable for most use cases
✅ Implement performance monitoring for production queries
✅ Consider caching for frequently accessed data
✅ Monitor query performance during peak usage times
```

## 🔧 Advanced Usage

### Test Specific Tenant
```bash
# List available tenants
node run-production-benchmarks.js --list

# Test specific tenant
node run-production-benchmarks.js devapi
node run-production-benchmarks.js staging3_dev
```

### Verify Before Testing
```bash
# Always verify first to catch issues early
node verify-production-databases.js

# Then run benchmarks
node run-production-benchmarks.js devapi
```

### Troubleshooting
```bash
# If verification fails, check:
# 1. Database connectivity
# 2. Stored procedure existence
# 3. Data availability
# 4. User permissions
```

## 📈 Understanding Production Results

### Performance Metrics
- **Average**: Mean execution time across 5 iterations
- **Min/Max**: Fastest and slowest execution times
- **Standard Deviation**: Consistency of performance
- **Success Rate**: Reliability of execution
- **Data Volume**: Number of rows returned

### Production Context
- **Work Items Count**: Total items in your database
- **Data Distribution**: Status breakdown, archived items, etc.
- **Use Case Performance**: Real-world query patterns
- **Statistical Accuracy**: Multiple iterations for reliability

### Performance Factors
1. **Data Volume**: Your actual production data size
2. **Database Load**: Current production activity
3. **Network Latency**: Distance to database server
4. **Hardware**: Production database server specs
5. **Indexes**: Your actual database optimization

## 🎯 Use Cases Tested

### 1. Dashboard Queries
- **Basic Production Fetch**: Simple work item lists
- **Job-Filtered Query**: Job-specific views
- **Package-Filtered Query**: Package management

### 2. Worker Interface
- **Assignment-Based Query**: Worker dashboard
- **BOM Items Query**: Material planning

### 3. Reporting & Analytics
- **Grouped Production Query**: Reports and analytics
- **Complex Production Query**: Advanced reporting
- **Archive-Inclusive Query**: Historical analysis

## 📊 Production Data Analysis

The framework analyzes your production data:

```javascript
// Automatic data analysis
{
  workItems: 45234,
  jobs: 1247,
  packages: 3891,
  spools: 8456,
  archived: 2156,
  assigned: 12789,
  statusDistribution: [
    { status: 'completed', count: 23456 },
    { status: 'in_progress', count: 12789 },
    { status: 'pending', count: 8989 }
  ]
}
```

## 🔍 What Makes This "Real"

### 1. Actual Database Connections
```javascript
// Uses your actual database.config.ts
const config = getDatabaseConfig('devapi');
const connection = await mysql.createConnection({
  host: config.host,        // Your actual host
  port: config.port,        // Your actual port
  user: config.user,        // Your actual user
  password: config.password, // Your actual password
  database: config.database  // Your actual database
});
```

### 2. Real Stored Procedure Execution
```javascript
// Actual CALL statement on your database
const [results] = await connection.execute(
  'CALL sp_fetch_items(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
  scenario.params  // Real parameters
);
```

### 3. Production Data Volume
- Tests run on your actual work_items table
- Real relationships with jobs, packages, spools
- Actual data distribution and complexity
- Production-scale performance characteristics

### 4. Statistical Accuracy
- 5 iterations per test for reliability
- Standard deviation calculations
- Min/max/median analysis
- Success rate tracking

## 📁 Generated Files

### JSON Results File
```
production-benchmark-results-devapi-2024-12-10T15-45-23-123Z.json
```

Contains:
- Raw performance data
- Statistical analysis
- Production data context
- Environment information

### Markdown Report
```
PRODUCTION_BENCHMARK_REPORT_devapi_2024-12-10T15-45-23-123Z.md
```

Contains:
- Executive summary
- Performance rankings
- Use case analysis
- Production recommendations

## 🚨 Important Notes

### Data Safety
- **Read-only operations**: Only SELECT and CALL statements
- **No data modification**: Tests don't change your data
- **Production safe**: Designed for production database testing

### Performance Impact
- **Minimal load**: Tests run sequentially with delays
- **Short duration**: Each test takes 2-3 minutes total
- **Non-intrusive**: Won't impact production performance

### Prerequisites
- ✅ sp_fetch_items stored procedure must exist
- ✅ Database user needs SELECT and EXECUTE permissions
- ✅ Network access to production databases
- ✅ Node.js packages: mysql2, @prisma/client

## 🎉 Next Steps

1. **Run verification**: `node verify-production-databases.js`
2. **Run benchmarks**: `node run-production-benchmarks.js devapi`
3. **Review results**: Check generated reports
4. **Make decisions**: Choose best ORM for your use case
5. **Implement monitoring**: Set up production performance tracking

---

**This framework provides REAL performance data from your ACTUAL production databases, not simulated results. The numbers you get are genuine and can be trusted for production decisions.**
