#!/usr/bin/env node

/**
 * API Endpoint Testing Script
 * Tests if the NestJS application APIs are working correctly
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
const TENANT_ID = 'devapi';

class APITester {
  constructor() {
    this.results = [];
    this.startTime = new Date();
  }

  async testEndpoint(method, endpoint, data = null, description = '') {
    console.log(`🧪 Testing: ${method.toUpperCase()} ${endpoint}`);
    console.log(`   Description: ${description}`);
    
    const startTime = process.hrtime.bigint();
    
    try {
      const config = {
        method: method.toLowerCase(),
        url: `${BASE_URL}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
          'X-Tenant-Id': TENANT_ID
        },
        timeout: 10000
      };
      
      if (data && (method.toLowerCase() === 'post' || method.toLowerCase() === 'put')) {
        config.data = data;
      }
      
      const response = await axios(config);
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000;
      
      console.log(`   ✅ Success: ${response.status} ${response.statusText} (${duration.toFixed(2)}ms)`);
      console.log(`   📊 Response size: ${JSON.stringify(response.data).length} bytes`);
      
      this.results.push({
        method,
        endpoint,
        description,
        success: true,
        status: response.status,
        duration: duration,
        responseSize: JSON.stringify(response.data).length,
        error: null
      });
      
      return response.data;
      
    } catch (error) {
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000;
      
      if (error.response) {
        console.log(`   ❌ HTTP Error: ${error.response.status} ${error.response.statusText} (${duration.toFixed(2)}ms)`);
        console.log(`   📝 Error: ${error.response.data?.message || error.response.data}`);
      } else if (error.code === 'ECONNREFUSED') {
        console.log(`   ❌ Connection Error: Server not running (${duration.toFixed(2)}ms)`);
      } else {
        console.log(`   ❌ Error: ${error.message} (${duration.toFixed(2)}ms)`);
      }
      
      this.results.push({
        method,
        endpoint,
        description,
        success: false,
        status: error.response?.status || null,
        duration: duration,
        responseSize: 0,
        error: error.response?.data?.message || error.message
      });
      
      return null;
    }
    
    console.log('');
  }

  async testHealthEndpoints() {
    console.log('🏥 Testing Health & Basic Endpoints');
    console.log('=' .repeat(50));
    
    await this.testEndpoint('GET', '/', null, 'Root endpoint - basic health check');
    await this.testEndpoint('GET', '/health', null, 'Health check endpoint');
  }

  async testStoredProcedureEndpoints() {
    console.log('🗄️  Testing Stored Procedure Endpoints');
    console.log('=' .repeat(50));
    
    // Test basic stored procedure endpoints
    await this.testEndpoint('GET', '/stored-procedures/users', null, 'Get users via stored procedure');
    
    await this.testEndpoint('POST', '/stored-procedures/users', {
      name: 'Test User',
      email: '<EMAIL>',
      status: true
    }, 'Create user via stored procedure');
    
    await this.testEndpoint('GET', '/stored-procedures/users/1', null, 'Get user by ID via stored procedure');
    
    // Test sp_fetch_items endpoint
    await this.testEndpoint('POST', '/stored-procedures/fetch-items', {
      groupHow: 0,
      includeArchived: false,
      appType: 'fab'
    }, 'Test sp_fetch_items stored procedure');
  }

  async testPrismaEndpoints() {
    console.log('🔷 Testing Prisma Endpoints');
    console.log('=' .repeat(50));
    
    await this.testEndpoint('GET', '/prisma/users', null, 'Get users via Prisma');
    
    await this.testEndpoint('POST', '/prisma/users', {
      name: 'Prisma Test User',
      email: '<EMAIL>',
      status: true
    }, 'Create user via Prisma');
    
    await this.testEndpoint('GET', '/prisma/users/1', null, 'Get user by ID via Prisma');
    
    // Test Prisma stored procedure
    await this.testEndpoint('POST', '/prisma/users/sp/fetch-items', {
      groupHow: 0,
      includeArchived: false,
      appType: 'fab'
    }, 'Test Prisma sp_fetch_items');
  }

  async testTypeORMEndpoints() {
    console.log('🔶 Testing TypeORM Endpoints');
    console.log('=' .repeat(50));
    
    await this.testEndpoint('GET', '/typeorm/users', null, 'Get users via TypeORM');
    
    await this.testEndpoint('POST', '/typeorm/users', {
      name: 'TypeORM Test User',
      email: '<EMAIL>',
      status: true
    }, 'Create user via TypeORM');
    
    await this.testEndpoint('GET', '/typeorm/users/1', null, 'Get user by ID via TypeORM');
  }

  async testMikroORMEndpoints() {
    console.log('🔸 Testing MikroORM Endpoints');
    console.log('=' .repeat(50));
    
    await this.testEndpoint('GET', '/mikroorm/users', null, 'Get users via MikroORM');
    
    await this.testEndpoint('POST', '/mikroorm/users', {
      name: 'MikroORM Test User',
      email: '<EMAIL>',
      status: true
    }, 'Create user via MikroORM');
    
    await this.testEndpoint('GET', '/mikroorm/users/1', null, 'Get user by ID via MikroORM');
  }

  async testBenchmarkEndpoints() {
    console.log('📊 Testing Benchmark Endpoints');
    console.log('=' .repeat(50));
    
    await this.testEndpoint('POST', '/benchmark/create-user', {
      name: 'Benchmark User',
      email: '<EMAIL>',
      status: true
    }, 'Benchmark user creation across all ORMs');
    
    await this.testEndpoint('GET', '/benchmark/users', null, 'Benchmark user retrieval across all ORMs');
    
    await this.testEndpoint('POST', '/benchmark/fetch-items', {
      groupHow: 0,
      includeArchived: false,
      appType: 'fab'
    }, 'Benchmark sp_fetch_items across all ORMs');
  }

  async checkServerStatus() {
    console.log('🔍 Checking Server Status...');
    
    try {
      const response = await axios.get(`${BASE_URL}/`, { timeout: 5000 });
      console.log('✅ Server is running and responding');
      return true;
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log('❌ Server is not running or not accessible');
        console.log('💡 Start the server with: npm run start:dev');
      } else {
        console.log(`❌ Server error: ${error.message}`);
      }
      return false;
    }
    
    console.log('');
  }

  generateReport() {
    const endTime = new Date();
    const duration = (endTime - this.startTime) / 1000;
    
    console.log('📋 API TESTING REPORT');
    console.log('=' .repeat(50));
    console.log(`🕒 Test Duration: ${duration.toFixed(1)} seconds`);
    console.log(`🎯 Total Tests: ${this.results.length}`);
    
    const successful = this.results.filter(r => r.success);
    const failed = this.results.filter(r => !r.success);
    
    console.log(`✅ Successful: ${successful.length}`);
    console.log(`❌ Failed: ${failed.length}`);
    console.log('');
    
    if (successful.length > 0) {
      console.log('✅ SUCCESSFUL ENDPOINTS:');
      successful.forEach(result => {
        console.log(`   ${result.method.toUpperCase()} ${result.endpoint} - ${result.duration.toFixed(2)}ms`);
      });
      console.log('');
    }
    
    if (failed.length > 0) {
      console.log('❌ FAILED ENDPOINTS:');
      failed.forEach(result => {
        console.log(`   ${result.method.toUpperCase()} ${result.endpoint} - ${result.error}`);
      });
      console.log('');
    }
    
    // Performance analysis
    if (successful.length > 0) {
      const avgDuration = successful.reduce((sum, r) => sum + r.duration, 0) / successful.length;
      const fastestEndpoint = successful.reduce((min, r) => r.duration < min.duration ? r : min);
      const slowestEndpoint = successful.reduce((max, r) => r.duration > max.duration ? r : max);
      
      console.log('📈 PERFORMANCE ANALYSIS:');
      console.log(`   Average Response Time: ${avgDuration.toFixed(2)}ms`);
      console.log(`   Fastest: ${fastestEndpoint.method.toUpperCase()} ${fastestEndpoint.endpoint} (${fastestEndpoint.duration.toFixed(2)}ms)`);
      console.log(`   Slowest: ${slowestEndpoint.method.toUpperCase()} ${slowestEndpoint.endpoint} (${slowestEndpoint.duration.toFixed(2)}ms)`);
      console.log('');
    }
    
    // Recommendations
    console.log('💡 RECOMMENDATIONS:');
    if (failed.length === this.results.length) {
      console.log('   🚨 All tests failed - check if server is running');
      console.log('   🔧 Start server: npm run start:dev');
      console.log('   🗄️  Check database connection');
    } else if (failed.length > 0) {
      console.log('   ⚠️  Some endpoints failed - check database connectivity');
      console.log('   🔍 Review error messages above');
    } else {
      console.log('   🎉 All endpoints working correctly!');
      console.log('   📊 Ready for performance benchmarking');
    }
  }

  async runAllTests() {
    console.log('🚀 API Endpoint Testing Suite');
    console.log(`🏢 Tenant: ${TENANT_ID}`);
    console.log(`🌐 Base URL: ${BASE_URL}`);
    console.log('=' .repeat(50));
    console.log('');
    
    const serverRunning = await this.checkServerStatus();
    
    if (!serverRunning) {
      console.log('⚠️  Server is not running. Some tests will fail.');
      console.log('   Start the server and run this script again for full testing.');
      console.log('');
    }
    
    // Run all test suites
    await this.testHealthEndpoints();
    await this.testStoredProcedureEndpoints();
    await this.testPrismaEndpoints();
    await this.testTypeORMEndpoints();
    await this.testMikroORMEndpoints();
    await this.testBenchmarkEndpoints();
    
    this.generateReport();
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help')) {
    console.log('API Endpoint Testing Script');
    console.log('');
    console.log('Usage:');
    console.log('  node test-api-endpoints.js           # Run all API tests');
    console.log('  node test-api-endpoints.js --help    # Show this help');
    console.log('');
    console.log('Prerequisites:');
    console.log('  1. Start the NestJS application: npm run start:dev');
    console.log('  2. Ensure database is accessible (for full testing)');
    console.log('');
    console.log('This script will test:');
    console.log('  - Health endpoints');
    console.log('  - Stored procedure endpoints');
    console.log('  - Prisma endpoints');
    console.log('  - TypeORM endpoints');
    console.log('  - MikroORM endpoints');
    console.log('  - Benchmark endpoints');
    return;
  }

  const tester = new APITester();
  await tester.runAllTests();
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ Test suite failed:', error.message);
    process.exit(1);
  });
}

module.exports = { APITester };
