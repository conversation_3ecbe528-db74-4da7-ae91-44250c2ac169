# SP_FETCH_ITEMS Testing Guide

This guide explains how to test the complex `sp_fetch_items` stored procedure across different ORM implementations and provides detailed flow diagrams.

## Overview

The `sp_fetch_items` stored procedure is a complex database operation that fetches work items with advanced filtering, grouping, and joining capabilities. It demonstrates the performance differences between ORM approaches and direct stored procedure calls.

## Architecture Flow

```mermaid
graph LR
    A[Client Request] --> B[NestJS Controller]
    B --> C{Implementation Type}
    C -->|Direct| D[StoredProcedureService]
    C -->|TypeORM| E[TypeORM Service]
    C -->|MikroORM| F[MikroORM Service]
    C -->|Prisma| G[Prisma Service]
    C -->|Benchmark| H[Benchmark Controller]
    
    D --> I[MySQL2 Connection]
    E --> J[TypeORM DataSource]
    F --> K[MikroORM EntityManager]
    G --> L[Prisma Client]
    H --> M[All Implementations]
    
    I --> N[(Database)]
    J --> N
    K --> N
    L --> N
    M --> N
    
    N --> O[sp_fetch_items Execution]
    O --> P[Results + Performance Metrics]
```

## Stored Procedure Parameters

The `sp_fetch_items` procedure accepts the following parameters:

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `a_work_item_column_ids` | TEXT | Comma-separated column IDs for grouping | `"1,2,3"` |
| `a_job_ids` | TEXT | Comma-separated job IDs to filter | `"10,20,30"` |
| `a_package_ids` | TEXT | Comma-separated package IDs to filter | `"100,200"` |
| `a_drawing_ids` | TEXT | Comma-separated drawing/spool IDs | `"1000,2000"` |
| `a_stage_ids` | TEXT | Comma-separated stage IDs | `"5,6,7"` |
| `a_bom` | TINYINT(1) | Include BOM items only | `1` or `0` |
| `a_is_assigned` | TINYINT(1) | Filter by assignment status | `1` or `0` |
| `a_include_archived` | TINYINT(1) | Include archived items | `1` or `0` |
| `a_current_user` | INT | Current user ID for assignments | `123` |
| `a_default_round` | INT | Default rounding rule | `1` |
| `a_group_how` | INT | Grouping method (0-3) | `0` |
| `a_app_type` | VARCHAR(5) | Application type | `"fab"` |

## Testing Endpoints

### 1. Direct Stored Procedure Call
```
POST /stored-procedures/fetch-items
GET  /stored-procedures/fetch-items
```

### 2. ORM-Specific Implementations
```
POST /typeorm/users/sp/fetch-items
POST /mikroorm/users/sp/fetch-items  
POST /prisma/users/sp/fetch-items
```

### 3. Comprehensive Benchmark
```
POST /benchmark/fetch-items
```

## Testing Scenarios

### Scenario 1: Basic Item Fetching (Ungrouped)

```bash
curl -X POST http://localhost:3000/benchmark/fetch-items \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: devapi" \
  -d '{
    "groupHow": 0,
    "includeArchived": false,
    "appType": "fab"
  }'
```

**Flow Diagram:**
```mermaid
sequenceDiagram
    participant C as Client
    participant BC as Benchmark Controller
    participant DS as Direct SP Service
    participant TS as TypeORM Service
    participant MS as MikroORM Service
    participant PS as Prisma Service
    participant DB as Database
    
    C->>BC: POST /benchmark/fetch-items
    BC->>DS: callStoredProcedure()
    DS->>DB: CALL sp_fetch_items(...)
    DB-->>DS: Results + Timing
    
    BC->>TS: callStoredProcedure()
    TS->>DB: CALL sp_fetch_items(...)
    DB-->>TS: Results + Timing
    
    BC->>MS: callStoredProcedure()
    MS->>DB: CALL sp_fetch_items(...)
    DB-->>MS: Results + Timing
    
    BC->>PS: callStoredProcedure()
    PS->>DB: CALL sp_fetch_items(...)
    DB-->>PS: Results + Timing
    
    BC-->>C: Performance Comparison
```

### Scenario 2: Filtered by Job IDs

```bash
curl -X POST http://localhost:3000/benchmark/fetch-items \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: devapi" \
  -d '{
    "jobIds": "1,2,3",
    "groupHow": 0,
    "includeArchived": false,
    "appType": "fab"
  }'
```

### Scenario 3: Grouped by Columns

```bash
curl -X POST http://localhost:3000/benchmark/fetch-items \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: devapi" \
  -d '{
    "workItemColumnIds": "1,2,3",
    "groupHow": 1,
    "includeArchived": false,
    "appType": "fab"
  }'
```

### Scenario 4: Complex Filtering with Assignments

```bash
curl -X POST http://localhost:3000/benchmark/fetch-items \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: devapi" \
  -d '{
    "jobIds": "1,2",
    "packageIds": "10,20",
    "stageIds": "5,6",
    "isAssigned": true,
    "currentUser": 123,
    "bom": true,
    "groupHow": 2,
    "includeArchived": false,
    "appType": "fab"
  }'
```

## Stored Procedure Internal Flow

```mermaid
flowchart TD
    A[sp_fetch_items Called] --> B[Parameter Validation]
    B --> C[Initialize Variables]
    C --> D{Group How Check}
    
    D -->|0| E[Simple Grouping by ID]
    D -->|1| F[Group by Columns]
    D -->|2| G[Group by Columns + Drawing]
    D -->|3| H[Group by Material Properties]
    
    F --> I[Create Temporary Table]
    I --> J[Parse Column IDs]
    J --> K[Build Group By Clause]
    
    E --> L[Build Base Query]
    G --> L
    H --> L
    K --> L
    
    L --> M[Add Joins]
    M --> N{Assignment Filter?}
    N -->|Yes| O[Add Assignment Joins]
    N -->|No| P[Continue]
    O --> P
    
    P --> Q{Stage Filter?}
    Q -->|Yes| R[Add Stage Joins]
    Q -->|No| S[Continue]
    R --> S
    
    S --> T[Apply Filters]
    T --> U[Execute Dynamic Query]
    U --> V[Return Results]
```

## Performance Testing Script

Create a comprehensive test script:

```javascript
// test-sp-fetch-items.js
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
const TENANT_ID = 'devapi';

const headers = {
  'Content-Type': 'application/json',
  'X-Tenant-Id': TENANT_ID,
};

async function testSpFetchItems() {
  console.log('🔍 Testing sp_fetch_items Performance\n');

  const testCases = [
    {
      name: 'Basic Ungrouped Fetch',
      params: {
        groupHow: 0,
        includeArchived: false,
        appType: 'fab'
      }
    },
    {
      name: 'Filtered by Job IDs',
      params: {
        jobIds: '1,2,3',
        groupHow: 0,
        includeArchived: false,
        appType: 'fab'
      }
    },
    {
      name: 'Grouped by Columns',
      params: {
        workItemColumnIds: '1,2,3',
        groupHow: 1,
        includeArchived: false,
        appType: 'fab'
      }
    },
    {
      name: 'Complex Filtering',
      params: {
        jobIds: '1,2',
        packageIds: '10,20',
        stageIds: '5,6',
        isAssigned: true,
        currentUser: 123,
        bom: true,
        groupHow: 2,
        includeArchived: false,
        appType: 'fab'
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`📊 Testing: ${testCase.name}`);
    
    try {
      const response = await axios.post(
        `${BASE_URL}/benchmark/fetch-items`,
        testCase.params,
        { headers }
      );

      console.log('   Performance Results:');
      Object.entries(response.data.results).forEach(([method, result]) => {
        if (result.duration > 0) {
          console.log(`   ${method}: ${result.duration.toFixed(2)}ms`);
        }
      });
      console.log(`   🏆 Fastest: ${response.data.summary.fastest}`);
      console.log(`   📈 Performance Ratios:`, response.data.summary.performance_comparison);
      console.log('');
      
    } catch (error) {
      console.error(`   ❌ Error: ${error.message}`);
    }
  }
}

testSpFetchItems();
```

## Expected Performance Results

Based on the complexity of sp_fetch_items:

```mermaid
graph LR
    A[Direct SP<br/>~50ms] --> B[Prisma SP<br/>~65ms]
    B --> C[TypeORM SP<br/>~75ms]
    C --> D[MikroORM SP<br/>~85ms]
    
    style A fill:#90EE90
    style B fill:#FFE4B5
    style C fill:#FFB6C1
    style D fill:#FFA07A
```

## Database Setup Requirements

Ensure your database has the required tables and stored procedures:

```sql
-- Required tables for sp_fetch_items
-- work_items, Jobs, Packages, Spools, work_item_stages, etc.
-- Run the sp_fetch_items.sql script in your database
```

## Troubleshooting

### Common Issues:

1. **Missing Tables**: Ensure all referenced tables exist
2. **Missing Stored Procedure**: Run the sp_fetch_items.sql script
3. **Permission Issues**: Ensure database user has EXECUTE permissions
4. **Tenant Configuration**: Verify tenant exists in database config

### Debug Mode:

Enable detailed logging by setting environment variable:
```bash
DEBUG=true npm run start:dev
```

## Monitoring and Analysis

The benchmark results provide insights into:
- **Query Complexity Impact**: How filtering affects performance
- **ORM Overhead**: Performance cost of each ORM layer
- **Grouping Performance**: Impact of different grouping strategies
- **Connection Efficiency**: How each ORM manages database connections

Use these insights to optimize your application's data access patterns.

## Quick Start Testing

### 1. Start the Application
```bash
cd orm-comparison
npm run start:dev
```

### 2. Run Quick Performance Test
```bash
node test-sp-fetch-items.js --quick
```

### 3. Run Comprehensive Tests
```bash
node test-sp-fetch-items.js
```

### 4. Test Specific Scenarios
```bash
# Test basic ungrouped fetch
curl -X POST http://localhost:3000/benchmark/fetch-items \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: devapi" \
  -d '{"groupHow": 0, "includeArchived": false, "appType": "fab"}'

# Test with job filtering
curl -X POST http://localhost:3000/benchmark/fetch-items \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: devapi" \
  -d '{"jobIds": "1,2,3", "groupHow": 0, "includeArchived": false, "appType": "fab"}'
```

## Performance Visualization

### Typical Performance Hierarchy
```mermaid
graph TD
    A[Direct MySQL2<br/>Connection] --> B[Fastest<br/>~50-80ms]
    C[Prisma Raw Query] --> D[Good<br/>~65-100ms]
    E[TypeORM Raw Query] --> F[Moderate<br/>~75-120ms]
    G[MikroORM Raw Query] --> H[Slower<br/>~85-140ms]

    style A fill:#90EE90,stroke:#006400,stroke-width:3px
    style C fill:#FFE4B5,stroke:#FF8C00,stroke-width:2px
    style E fill:#FFB6C1,stroke:#DC143C,stroke-width:2px
    style G fill:#FFA07A,stroke:#FF4500,stroke-width:2px
```

### Performance Impact by Complexity
```mermaid
graph LR
    A[Simple Fetch<br/>No Filters] --> B[+0ms baseline]
    C[Job Filtering] --> D[+10-20ms]
    E[Multiple Filters] --> F[+20-40ms]
    G[Grouping] --> H[+30-60ms]
    I[Complex Grouping<br/>+ Multiple Filters] --> J[+50-100ms]

    style A fill:#90EE90
    style C fill:#FFFF99
    style E fill:#FFE4B5
    style G fill:#FFB6C1
    style I fill:#FFA07A
```

## Data Flow Analysis

### Request Processing Flow
```mermaid
sequenceDiagram
    participant Client
    participant Controller
    participant Service
    participant Connection
    participant Database
    participant SP as sp_fetch_items

    Client->>Controller: POST /benchmark/fetch-items
    Note over Controller: Parse parameters
    Controller->>Service: Multiple implementations

    par Direct SP
        Service->>Connection: MySQL2 connection
        Connection->>Database: CALL sp_fetch_items(...)
        Database->>SP: Execute procedure
        SP->>Database: Complex query execution
        Database-->>Connection: Results
        Connection-->>Service: Results + timing
    and TypeORM SP
        Service->>Connection: TypeORM DataSource
        Connection->>Database: CALL sp_fetch_items(...)
        Database-->>Connection: Results
        Connection-->>Service: Results + timing
    and MikroORM SP
        Service->>Connection: MikroORM EntityManager
        Connection->>Database: CALL sp_fetch_items(...)
        Database-->>Connection: Results
        Connection-->>Service: Results + timing
    and Prisma SP
        Service->>Connection: Prisma Client
        Connection->>Database: CALL sp_fetch_items(...)
        Database-->>Connection: Results
        Connection-->>Service: Results + timing
    end

    Service-->>Controller: Aggregated results
    Controller-->>Client: Performance comparison
```

## Advanced Testing Scenarios

### Load Testing
```bash
# Install artillery for load testing
npm install -g artillery

# Create artillery config
cat > artillery-config.yml << EOF
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
  defaults:
    headers:
      X-Tenant-Id: 'devapi'
      Content-Type: 'application/json'

scenarios:
  - name: 'sp_fetch_items load test'
    requests:
      - post:
          url: '/benchmark/fetch-items'
          json:
            groupHow: 0
            includeArchived: false
            appType: 'fab'
EOF

# Run load test
artillery run artillery-config.yml
```

### Memory Usage Monitoring
```bash
# Monitor memory usage during tests
node --inspect test-sp-fetch-items.js
```

## Optimization Recommendations

### Database Level
1. **Indexes**: Ensure proper indexes on filtered columns
2. **Statistics**: Keep table statistics updated
3. **Query Cache**: Enable MySQL query cache for repeated calls
4. **Connection Pooling**: Optimize connection pool sizes

### Application Level
1. **Connection Reuse**: Implement proper connection pooling
2. **Parameter Caching**: Cache frequently used parameter combinations
3. **Result Caching**: Implement Redis caching for stable results
4. **Batch Processing**: Group multiple calls when possible

### Monitoring Setup
```javascript
// Add to your monitoring setup
const performanceMetrics = {
  'sp_fetch_items_direct': [],
  'sp_fetch_items_typeorm': [],
  'sp_fetch_items_mikroorm': [],
  'sp_fetch_items_prisma': []
};

// Track metrics over time
function trackPerformance(method, duration) {
  performanceMetrics[method].push({
    timestamp: new Date(),
    duration: duration
  });
}
```

Use these insights to optimize your application's data access patterns.
