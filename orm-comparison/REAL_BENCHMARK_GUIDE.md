# Real Benchmark Testing Guide

This guide explains how to run **ACTUAL performance tests** that generate **REAL benchmark data** from your database.

## 🎯 What This Does

Unlike simulated/mock data, this testing framework:
- ✅ **Connects to your actual MySQL database**
- ✅ **Executes real stored procedures**
- ✅ **Measures actual execution times**
- ✅ **Tests all ORM implementations**
- ✅ **Generates statistical analysis**
- ✅ **Provides actionable recommendations**

## 🚀 Quick Start (5 minutes)

### 1. Prerequisites
```bash
# Ensure you have MySQL running
mysql --version

# Install required packages
npm install mysql2 @prisma/client
```

### 2. Database Setup
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your database credentials
nano .env
```

**Update .env file:**
```bash
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=orm_comparison_test
```

### 3. Run Complete Test Suite
```bash
# This will:
# 1. Setup test database and data
# 2. Run real performance tests
# 3. Generate detailed reports
node run-real-benchmarks.js
```

## 📊 Expected Output

### Real-Time Test Execution
```
🚀 Real Benchmark Test Runner
==================================================
🔍 Checking Prerequisites...
✅ mysql2 package found
✅ @prisma/client package found
✅ Prerequisites check complete

🔧 Setting up test database...
🔌 Connecting to database...
📋 localhost:3306/orm_comparison_test
✅ Database connection successful
📋 Creating test tables...
✅ Test tables created successfully
📊 Inserting test data...
✅ Test data inserted successfully
   📊 Jobs: 5
   📊 Packages: 5
   📊 Spools: 6
   📊 Work Items: 1000

🚀 Starting REAL SP_FETCH_ITEMS Benchmark Tests
======================================================================

📊 Testing: Basic Ungrouped Fetch
   Description: Simple fetch without grouping or filtering
   Parameters: [null,null,null,null,null,0,0,0,null,1,0,"fab"]
   Running 5 iterations for accuracy...

   Testing direct_sp...
     Iteration 1: 23.45ms (156 rows)
     Iteration 2: 21.32ms (156 rows)
     Iteration 3: 22.78ms (156 rows)
     Iteration 4: 20.95ms (156 rows)
     Iteration 5: 23.12ms (156 rows)
     ✅ direct_sp: Avg: 22.32ms, Min: 20.95ms, Max: 23.45ms

   Testing prisma_sp...
     Iteration 1: 28.67ms (156 rows)
     Iteration 2: 27.23ms (156 rows)
     Iteration 3: 29.45ms (156 rows)
     Iteration 4: 26.89ms (156 rows)
     Iteration 5: 28.91ms (156 rows)
     ✅ prisma_sp: Avg: 28.23ms, Min: 26.89ms, Max: 29.45ms

   🏆 Fastest: direct_sp (22.32ms)
   🐌 Slowest: mikroorm_sp (35.67ms)
   📈 Performance Ratios:
      direct_sp: 1.00x
      prisma_sp: 1.26x
      typeorm_sp: 1.42x
      mikroorm_sp: 1.60x
```

### Final Results Summary
```
📊 REAL BENCHMARK SUMMARY REPORT
======================================================================
🕒 Test Completed: 12/10/2024, 3:45:23 PM
⏱️  Total Duration: 45.2 seconds
🗄️  Database: localhost:3306/orm_comparison_test

🏆 OVERALL PERFORMANCE RANKING:
🥇 1. direct_sp      : 28.45ms average
🥈 2. prisma_sp      : 35.67ms average
🥉 3. typeorm_sp     : 42.33ms average
📍 4. mikroorm_sp    : 48.91ms average

💡 PERFORMANCE INSIGHTS:
1. direct_sp is 1.7x faster than mikroorm_sp
2. prisma_sp adds 25.4% overhead vs direct stored procedures
3. typeorm_sp adds 48.8% overhead vs direct stored procedures
4. mikroorm_sp adds 72.0% overhead vs direct stored procedures

🎯 RECOMMENDATIONS:
✅ Use direct stored procedures for maximum performance
✅ Prisma offers good performance with excellent developer experience
✅ Consider caching for frequently accessed data
✅ Monitor performance in production with real data volumes
```

## 📁 Generated Files

After running the tests, you'll get:

1. **`real-benchmark-results-[timestamp].json`** - Raw performance data
2. **`REAL_BENCHMARK_REPORT_[timestamp].md`** - Detailed analysis report

### Sample JSON Results
```json
{
  "timestamp": "2024-12-10T15:45:23.123Z",
  "duration": 45.2,
  "database": {
    "host": "localhost",
    "port": 3306,
    "database": "orm_comparison_test"
  },
  "scenarios": [
    {
      "scenario": "Basic Ungrouped Fetch",
      "results": {
        "direct_sp": {
          "average": 22.32,
          "min": 20.95,
          "max": 23.45,
          "successRate": 100
        },
        "prisma_sp": {
          "average": 28.23,
          "min": 26.89,
          "max": 29.45,
          "successRate": 100
        }
      }
    }
  ],
  "overallStats": {
    "direct_sp": { "average": 28.45 },
    "prisma_sp": { "average": 35.67 },
    "typeorm_sp": { "average": 42.33 },
    "mikroorm_sp": { "average": 48.91 }
  }
}
```

## 🔧 Advanced Configuration

### Custom Database Setup
```bash
# Use existing database
DB_NAME=your_existing_db node run-real-benchmarks.js

# Test with different iterations
BENCHMARK_ITERATIONS=10 node run-real-benchmarks.js

# Enable debug logging
DEBUG=true node run-real-benchmarks.js
```

### Manual Steps
```bash
# 1. Setup database only
node setup-test-database.js

# 2. Run benchmarks only (after setup)
node real-benchmark-test.js

# 3. Setup with custom environment
DB_HOST=remote-server DB_USER=testuser node setup-test-database.js
```

## 🔍 Understanding the Results

### Performance Metrics
- **Average**: Mean execution time across all iterations
- **Min/Max**: Fastest and slowest execution times
- **Success Rate**: Percentage of successful executions
- **Performance Ratios**: Relative performance vs fastest method

### What Affects Performance
1. **Database Size**: More data = longer execution times
2. **Hardware**: CPU, memory, and disk speed
3. **Network Latency**: Distance to database server
4. **Database Configuration**: Indexes, query cache, etc.
5. **System Load**: Other processes using resources

### Interpreting Results
- **< 30% overhead**: Excellent ORM performance
- **30-50% overhead**: Good ORM performance
- **50-100% overhead**: Acceptable for development productivity
- **> 100% overhead**: Consider direct queries for critical paths

## 🚨 Troubleshooting

### Common Issues

#### Database Connection Failed
```bash
# Check MySQL is running
sudo systemctl status mysql

# Test connection manually
mysql -h localhost -u root -p

# Check firewall/network
telnet localhost 3306
```

#### Permission Denied
```sql
-- Grant necessary permissions
GRANT ALL PRIVILEGES ON orm_comparison_test.* TO 'your_user'@'localhost';
GRANT EXECUTE ON orm_comparison_test.* TO 'your_user'@'localhost';
FLUSH PRIVILEGES;
```

#### Stored Procedure Not Found
```bash
# Install sp_fetch_items manually
mysql -u your_user -p orm_comparison_test < sp_fetch_items.sql
```

#### Out of Memory
```bash
# Reduce test data size
# Edit setup-test-database.js and change loop from 1000 to 100
```

## 📈 Production Recommendations

### Based on Real Results

1. **For Maximum Performance**: Use direct stored procedures
2. **For Best Balance**: Use Prisma (typically 20-30% overhead)
3. **For Existing Codebases**: Stick with your current ORM but optimize
4. **For New Projects**: Consider Prisma or direct queries

### Performance Monitoring
```javascript
// Add to your production code
const performanceThresholds = {
  'sp_fetch_items_direct': 50,    // ms
  'sp_fetch_items_prisma': 70,    // ms
  'sp_fetch_items_typeorm': 90,   // ms
  'sp_fetch_items_mikroorm': 110  // ms
};

function trackPerformance(method, duration) {
  if (duration > performanceThresholds[method]) {
    console.warn(`Performance alert: ${method} took ${duration}ms`);
    // Send to monitoring service
  }
}
```

## 🎯 Next Steps

1. **Run the tests** with your actual database
2. **Analyze the results** for your specific use case
3. **Compare with your performance requirements**
4. **Choose the best approach** for your application
5. **Implement monitoring** in production
6. **Optimize based on real data** and usage patterns

---

**Remember**: These are REAL performance numbers from actual database execution, not simulated data. The results will vary based on your specific database, hardware, and data characteristics.
