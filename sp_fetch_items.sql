CREATE DEFINER=`schema`@`%` PROCEDURE `devapi`.`sp_fetch_items`(
    IN a_work_item_column_ids TEXT
    ,IN a_job_ids TEXT
    ,IN a_package_ids TEXT
    ,IN a_drawing_ids TEXT
    ,IN a_stage_ids TEXT
    ,IN a_bom TINYINT(1)
    ,IN a_is_assigned TINYINT(1)
    ,IN a_include_archived TINYINT(1)
    ,IN a_current_user INT(10) UNSIGNED
    ,IN a_default_round INT
    ,IN a_group_how INT     ## 0 for ungrouped, 1 for grouped by columns sent , 2 for grouped by columns sent and by drawing, 3 for grouped as in V2 materials
    ,IN a_app_type VARCHAR(5)
)
this_sproc: BEGIN

    ## name: sp_fetch_items
    ## purpose: to fetch items by filters
    ## WARNING: Any time this sproc is changed, we MUST double check ALL the front end places that use it, especially in grouping mode!
    
    DECLARE v_work_item_column_ids TEXT;
    DECLARE v_job_ids TEXT;
    DECLARE v_package_ids TEXT;
    DECLARE v_drawing_ids TEXT;
    DECLARE v_stage_ids TEXT;
    DECLARE v_bom TINYINT(1);
    DECLARE v_is_assigned TINYINT(1);
    DECLARE v_include_archived TINYINT(1);
    DECLARE v_current_user INT(10) UNSIGNED;
    DECLARE v_default_round INT;
    DECLARE v_group_how INT;
    DECLARE v_app_type VARCHAR(10);

    DECLARE v_select TEXT;
    DECLARE v_select_with TEXT;
    DECLARE v_join TEXT;
    DECLARE v_where TEXT;
    DECLARE v_where_with TEXT;
    DECLARE v_group_by TEXT;

    SET v_work_item_column_ids = CASE WHEN a_work_item_column_ids IN ('null', '') THEN NULL ELSE a_work_item_column_ids END;
    SET v_job_ids = CASE WHEN a_job_ids IN ('null', '') THEN NULL ELSE a_job_ids END;
    SET v_package_ids = CASE WHEN a_package_ids IN ('null', '') THEN NULL ELSE a_package_ids END;
    SET v_drawing_ids = CASE WHEN a_drawing_ids IN ('null', '') THEN NULL ELSE a_drawing_ids END;
    SET v_stage_ids = CASE WHEN a_stage_ids IN ('null', '') THEN NULL ELSE a_stage_ids END;
    SET v_bom = a_bom;
    SET v_is_assigned = a_is_assigned;
    SET v_include_archived = CASE WHEN a_include_archived IN (1) THEN a_include_archived ELSE 0 END;
    SET v_current_user = a_current_user;
    SET v_default_round = a_default_round;
    SET v_group_how = a_group_how;
    SET v_app_type = IFNULL(a_app_type,'fab');

    SET v_select = '';
    SET v_select_with = '';
    SET v_join = '';
    SET v_where = '';
    SET v_where_with = '';
    SET v_group_by = '';

    IF v_group_how IS NULL
    THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT='grouped cannot be null';
        LEAVE this_sproc;
    END IF;

    -- if grouping, always grab the qty, divide if system setting fractional ft/in
    IF v_group_how != 0
    THEN
        SET @fractional = (SELECT fractional_display FROM SystemSettings WHERE CompanyID = 1);
        SET v_select = CONCAT( v_select, ' ,CEIL((CASE WHEN A.is_cut = 1
													THEN CASE WHEN ',@fractional,' = 1 THEN SUM(A.length)/12 ELSE SUM(A.length) END
                                                    ELSE COUNT(DISTINCT A.id) END) * POW(10,5))/POW(10,5) AS quantity ');

    END IF;

    IF v_group_how = 0
    THEN

        SET v_group_by = ' GROUP BY A.id ';

    ELSEIF v_group_how = 1 OR v_group_how = 2
    THEN

        IF v_work_item_column_ids IS NULL
        THEN
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT='Column ids are required';
        END IF;

        DROP TEMPORARY TABLE IF EXISTS `item_columns`;
        CREATE TEMPORARY TABLE `item_columns` (
            `column_id` INT(10) UNSIGNED
            ,`name` TEXT
            ,`table_name` TEXT
            ,`group_by` TEXT
        );

        CALL `sp_split_string_ids`(a_work_item_column_ids, ',', 'split_items');

        INSERT INTO `item_columns` ( `column_id`, `name`, `table_name` )
            SELECT
            WIC.id
             ,COALESCE(WIC.`fk_column`,WIC.`name`)
             ,WIC.`table_source`
            FROM `split_items`
                 JOIN `work_item_columns` WIC
                 ON WIC.`id` = `split_items`.`str_part`
            WHERE WIC.`table_source` != 'joint_heat_numbers'
            AND WIC.table_target = 'work_items';

        UPDATE `item_columns` SET `group_by` = CONCAT('A.', `name`);

        SET v_group_by = (SELECT CONCAT(' GROUP BY '
                                     , (CASE WHEN v_group_how = 2 THEN ' A.drawing_id,' WHEN v_group_how = 1 THEN '' END)
                                     , GROUP_CONCAT(group_by)
                                     ,',A.rejected')
                              FROM item_columns);

        IF EXISTS(SELECT WIC.table_source	FROM split_items JOIN work_item_columns WIC ON WIC.id = split_items.str_part WHERE WIC.table_source = 'joint_heat_numbers')
        THEN
            SET v_group_by =  CONCAT(v_group_by, ' ,A.joint_heat_numbers ');
        END IF;

        DROP TEMPORARY TABLE IF EXISTS item_columns;

    ELSEIF v_group_how = 3
    THEN
        SET v_group_by = ' GROUP BY A.job_id, A.package_id, A.heat_number, A.vendor, A.material_type_id, A.size ';
    END IF;

    IF v_is_assigned = 1
    THEN
        SET v_join = CONCAT( v_join,  ' LEFT JOIN SpoolAssignments SA
										ON SA.SpoolID = WI.drawing_id
										AND SA.UserID IN (', v_current_user, ')
										LEFT JOIN PackageAssignments PA
											ON PA.PackageID = WI.package_id
											AND PA.UserID IN (', v_current_user, ')
										LEFT JOIN JobAssignments JA
										ON JA.JobID = WI.job_id
										AND JA.UserID IN (', v_current_user, ') ');

        SET v_where_with = CONCAT( v_where_with, ' AND COALESCE(SA.SpoolID,PA.PackageID,JA.JobID,0) != 0 ');

    END IF;

    IF v_stage_ids IS NULL
    THEN
        SET v_select_with = CONCAT(v_select_with, ' ,NULL AS available
												  ,NULL AS completed
                                                  ,\'Not Completed\' AS completedness');

    ELSEIF v_stage_ids = '0'
    THEN
        SET v_select_with = CONCAT(v_select_with, ',NULL AS available
												 ,NULL AS completed
                                                  ,\'Not Completed\' AS completedness ');

        SET v_join = CONCAT(v_join, ' LEFT JOIN work_item_stages WIS
											ON WIS.work_item_id = WI.id
											AND WIS.deleted = 0');

        SET v_where_with =  CONCAT(v_where_with, ' AND WIS.id IS NULL ');

    ELSEIF v_stage_ids IS NOT NULL
    THEN
        SET v_select_with = CONCAT(v_select_with, ' ,CASE WHEN COUNT(DISTINCT WIS.id) = 1 THEN WIS.available ELSE NULL END AS available
												   ,CASE WHEN COUNT(DISTINCT WIS.id) = 1 THEN WIS.completed ELSE NULL END AS completed
												 ,CASE WHEN MIN(IFNULL(WIS.completed, 0)) = 0 THEN \'Not Completed\' ELSE \'Completed\' END AS completedness');

        SET v_join = CONCAT(v_join, ' JOIN work_item_stages WIS
											ON WIS.work_item_id = WI.id
											AND WIS.deleted = 0
                                            AND WIS.stage_id IN (', v_stage_ids, ') ');

        IF v_group_how != 0
        THEN
            SET v_group_by = CONCAT(v_group_by, ' ,A.available,A.completed ');
        END IF;

    END IF;

    IF v_drawing_ids IS NOT NULL
    THEN
        SET v_where_with = CONCAT(v_where_with, ' AND WI.drawing_id IN (', v_drawing_ids, ') ');
    END IF;

    IF v_package_ids IS NOT NULL
    THEN
        SET v_where_with = CONCAT(v_where_with, ' AND WI.package_id IN (', v_package_ids, ') ');
    END IF;

    IF v_job_ids IS NOT NULL
    THEN
        SET v_where_with = CONCAT(v_where_with, ' AND WI.job_id IN (', v_job_ids, ') ');
    END IF;

    IF v_bom = 1
    THEN
        SET v_where_with = CONCAT(v_where_with, ' AND MT.is_bomable = 1 ');
    END IF;

    IF v_include_archived = 0
    THEN
        SET v_where_with = CONCAT(v_where_with, ' AND WI.archived = 0 ');
    END IF;

    IF v_bom = 0
    THEN
        SET v_select_with = CONCAT(v_select_with, ' ,WI.revit_conduit_bend_id
                                                ,RCBI.type_id AS bend_type_id
                                                ,RCBI.bend_angle_1 AS bend_angle_1
                                                ,RCBI.bend_angle_2 AS bend_angle_2
                                                ,RCBI.bend_deduct AS bend_deduct
                                                ,RCBI.bend_dim_a AS bend_dim_a
                                                ,RCBI.bend_dim_b AS bend_dim_b
                                                ,RCBI.bend_dim_c AS bend_dim_c
                                                ,RCBI.bend_dim_d AS bend_dim_d
                                                ,RCBI.bend_dim_e AS bend_dim_e
                                                ,RCBI.bend_dim_f AS bend_dim_f
                                                ,RCBI.bend_dim_g AS bend_dim_g
                                                ,RCBI.bend_mark_1 AS bend_mark_1
                                                ,RCBI.bend_mark_2 AS bend_mark_2
                                                ,RCBI.bend_bender_type AS bend_bender_type ');

        SET v_select = CONCAT(v_select, ' ,A.revit_conduit_bend_id
                                        ,A.bend_type_id
                                        ,A.bend_angle_1
                                        ,A.bend_angle_2
                                        ,A.bend_deduct
                                        ,A.bend_dim_a
                                        ,A.bend_dim_b
                                        ,A.bend_dim_c
                                        ,A.bend_dim_d
                                        ,A.bend_dim_e
                                        ,A.bend_dim_f
                                        ,A.bend_dim_g
                                        ,A.bend_mark_1
                                        ,A.bend_mark_2
                                        ,A.bend_bender_type ');

        SET v_join = CONCAT(v_join, ' LEFT JOIN revit_conduit_bend_information RCBI
                                            ON RCBI.id = WI.revit_conduit_bend_id ');
    END IF;


    SET @default_rr = (SELECT imperial FROM rounding_rules WHERE `default` = 1);

    SET @sql = CONCAT('WITH A AS (
		SELECT WI.id
        ,J.id AS job_id
        ,J.JobTitle AS job_name
        ,J.JobNumber AS job_number
        ,P.id AS package_id
        ,P.PackageName AS package_name
        ,P.number AS package_number
        ,P.area AS package_area
        ,P.dueDate AS package_due_date
        ,P.priority AS package_priority
        ,S.id AS drawing_id
        ,S.SpoolName AS drawing_name
        ,S.area AS drawing_area
        ,S.dueDate AS drawing_due_date
        ,S.priority AS drawing_priority
		,S.on_hold
        ,S.pending_approval AS drawing_pending_approval
        ,WI.revit_item_id
        ,WI.tag_number
        ,WI.end_prep_1
		,WI.end_prep_2
		,WI.end_prep_3
		,WI.end_prep_4
        ,(CASE WHEN MT.is_cut = 1 THEN WI.rounded_cut_length ELSE WI.length END) AS length
        ,WI.rounded_cut_length
        ,WI.stock_length
		,WI.identifier
		,WI.measurement_area
		,WI.height
		,WI.width
		,WI.thickness
		,WI.paint_spec
		,WI.texture
		,WI.fixture_type
		,WI.gauge
		,WI.weight
		,WI.hanger_size
		,WI.product_code
		,WI.insulation
		,WI.insulation_area
		,WI.insulation_gauge
		,WI.insulation_specification
		,JP.name AS joining_procedure_name
        ,WI.joining_procedure_id
		,MT.name AS material_name
        ,WI.material_type_id
		,WI.service_name
		,WI.service_color_name
		,WI.status
		,WI.size
		,WI.area
		,WI.random_length
		,WI.rod_size
		,WI.support_rod_length
		,WI.support_rod_length_2
		,LL.name AS laydown_location_name
		,WI.liner_spec
		,WI.heat_number
        ,CASE WHEN JHN.id IS NOT NULL THEN
              CONCAT(\'[\',GROUP_CONCAT(DISTINCT JSON_OBJECT(\'position\', JHN.position, \'heat_number\', JHN.heat_number)),\']\')
            ELSE \'[]\' END AS joint_heat_numbers
		,C.name AS container_name
		,WI.archived
		,WI.filler_metal
		,WI.vendor
		,MT.is_cut AS is_cut
        ,MT.is_bomable
        ,CASE WHEN ',v_group_how,' != 0 THEN 0 ELSE CASE WHEN S.SpoolFile IS NULL THEN 0 ELSE 1 END END AS has_original
        ,CASE WHEN ',v_group_how,' != 0 THEN 0 ELSE CASE WHEN PDFA.drawing_id IS NULL THEN 0 ELSE 1 END END AS has_annotated
        ,CASE WHEN ',v_group_how,' != 0 THEN 0 ELSE CASE WHEN PM.active = 1 THEN 1 ELSE 0 END END AS has_package_map
		,FM.urn AS forge_urn
		,FM.model_name
        ,S.forge_model_id
		,COALESCE(PRR.imperial,MTRR.imperial,',@default_rr,') AS denominator
		,COALESCE(P.round_direction_id,MT.round_direction_id,',v_default_round,') AS round_direction_id
		,CASE WHEN R.resolved IS NULL THEN 0 ELSE 1 END AS rejected'
        ,v_select_with
        ,' 
        ,WI.material_gauge
        ,WI.material_thickness
           FROM work_items WI
	   LEFT JOIN Jobs J
		ON J.id = WI.job_id
		AND J.deleted = 0
	   LEFT JOIN Packages P
		ON P.id = WI.package_id
		AND P.deleted = 0
	  LEFT JOIN Spools S
		ON S.id = WI.drawing_id
		AND S.deleted = 0
      LEFT JOIN package_maps PM
		  ON WI.package_id = PM.package_id
		  AND PM.active = 1
      LEFT JOIN forge_models FM
		ON FM.id = S.forge_model_id
	  LEFT JOIN pdf_annotations PDFA
		  ON PDFA.drawing_id = WI.drawing_id
		  AND PDFA.is_active = 1
		  AND PDFA.type = \'added\'
	  LEFT JOIN material_types MT
		  ON MT.id = WI.material_type_id
	  LEFT JOIN laydown_locations LL
		  ON LL.id = WI.laydown_location_id
	  LEFT JOIN joining_procedures JP
		  ON JP.id = WI.joining_procedure_id
	  LEFT JOIN containers C
		  ON C.id = WI.shipping_container_id
	  LEFT JOIN rounding_rules AS PRR
		  ON PRR.id = P.rounding_rule_id
	  LEFT JOIN rounding_rules as MTRR
	  	  ON MTRR.id = MT.rounding_rule_id
	  LEFT JOIN joint_heat_numbers JHN
		  ON JHN.joint_work_item_id = WI.id
      LEFT JOIN rejections R
          ON R.work_item_id = WI.id
          AND R.resolved = 0 AND R.deleted = 0 '
        ,v_join,'
	WHERE WI.deleted = 0
	AND WI.',v_app_type,'= 1 '
        ,v_where_with
        ,' GROUP BY WI.id
    )
     SELECT
            A.id
            ,GROUP_CONCAT(DISTINCT A.id) AS work_item_ids
            ,A.job_id
            ,A.job_name
            ,A.job_number
            ,A.package_id
            ,A.package_name
            ,A.package_number
            ,A.package_area
            ,A.package_due_date
            ,A.package_priority
            ,A.drawing_id
            ,A.drawing_name
            ,A.drawing_area
            ,A.drawing_due_date
            ,A.drawing_priority
            ,A.drawing_pending_approval
            ,A.revit_item_id
            ,A.tag_number
            ,A.end_prep_1
            ,A.end_prep_2
            ,A.end_prep_3
            ,A.end_prep_4
            ,SUM(A.length) AS length
            ,A.stock_length
            ,A.identifier
            ,A.measurement_area
            ,A.height
            ,A.width
            ,A.thickness
            ,A.paint_spec
            ,A.texture
            ,A.fixture_type
            ,A.gauge
            ,A.weight
            ,A.hanger_size
            ,A.product_code
            ,A.insulation
            ,A.insulation_area
            ,A.insulation_gauge
            ,A.insulation_specification
            ,A.joining_procedure_name
            ,A.material_name
            ,A.service_name
            ,A.service_color_name
            ,A.status
            ,A.size
            ,A.area
            ,A.random_length
            ,A.rod_size
            ,A.support_rod_length
            ,A.support_rod_length_2
            ,A.laydown_location_name
            ,A.liner_spec
            ,A.heat_number
            ,A.joint_heat_numbers
            ,A.container_name
            ,A.archived
            ,A.filler_metal
            ,A.vendor
            ,A.completedness
            ,A.rejected
            ,A.is_cut
            ,NULL as original_url
            ,NULL as annotated_url
            ,NULL as package_map
            ,A.forge_urn
            ,A.model_name
            ,A.forge_model_id
            ,A.on_hold
            ,A.denominator
            ,A.round_direction_id
            ,A.available
            ,A.completed '
            ,v_select
            ,' 
            ,A.material_gauge
            ,A.material_thickness
            ,A.has_original
            ,A.has_annotated
            ,A.has_package_map
           FROM A '
        ,v_group_by
        );

    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    DROP TEMPORARY TABLE IF EXISTS item_columns;

END this_sproc