#!/usr/bin/env node

const { execSync } = require('child_process');

const REQUIRED_NODE_VERSION = 20;

function getCurrentNodeVersion() {
  return process.version;
}

function parseVersion(version) {
  // Remove 'v' prefix and get major version
  return parseInt(version.replace('v', '').split('.')[0]);
}

function isNodeVersionValid(currentVersion, requiredVersion) {
  const currentMajor = parseVersion(currentVersion);
  return currentMajor >= requiredVersion;
}

function switchToNodeV20() {
  try {
    console.log('🔄 Switching to Node.js v20 using nvm...');
    execSync('nvm use 20', { stdio: 'inherit' });
    console.log('✅ Successfully switched to Node.js v20');
    return true;
  } catch (error) {
    console.error('❌ Failed to switch to Node.js v20:', error.message);
    console.log('💡 Please install Node.js v20 using: nvm install 20');
    return false;
  }
}

function checkAndSwitchNodeVersion() {
  const currentVersion = getCurrentNodeVersion();
  console.log(`📋 Current Node.js version: ${currentVersion}`);
  console.log(`📋 Required Node.js version: v${REQUIRED_NODE_VERSION}.x`);

  if (isNodeVersionValid(currentVersion, REQUIRED_NODE_VERSION)) {
    console.log('✅ Node.js version is compatible');
    return true;
  }

  console.log('⚠️  Node.js version is not compatible');
  
  // Check if nvm is available
  try {
    execSync('nvm --version', { stdio: 'pipe' });
  } catch (error) {
    console.error('❌ nvm is not available. Please install nvm and Node.js v20');
    process.exit(1);
  }

  return switchToNodeV20();
}

// Run the check
if (require.main === module) {
  const success = checkAndSwitchNodeVersion();
  if (!success) {
    process.exit(1);
  }
}

module.exports = { checkAndSwitchNodeVersion };
